"""
简化的应用启动脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

try:
    print("正在启动小说写作专家应用...")
    
    from src.web.app import create_app
    from config import Config
    
    app = create_app()
    
    print(f"应用配置:")
    print(f"- DEBUG: {Config.FLASK_DEBUG}")
    print(f"- PORT: {Config.FLASK_PORT}")
    print(f"- 知识库路径: {Config.KNOWLEDGE_BASE_PATH}")
    
    print(f"\n应用启动成功！")
    print(f"请在浏览器中访问: http://localhost:{Config.FLASK_PORT}")
    print("按 Ctrl+C 停止应用")
    
    app.run(
        host='0.0.0.0',
        port=Config.FLASK_PORT,
        debug=Config.FLASK_DEBUG
    )
    
except Exception as e:
    print(f"应用启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
