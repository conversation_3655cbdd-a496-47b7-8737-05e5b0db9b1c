"""
测试优化后的功能
"""
import requests
import time

def test_plot_development():
    """测试情节发展功能"""
    print("🗺️ 测试情节发展功能...")
    
    data = {
        'current_plot': '主角李明刚刚发现了一个重大秘密：他的好友竟然是他一直在追查的神秘组织的成员。现在他不知道该相信谁，也不知道该如何处理这个信息。',
        'direction': '增加冲突',
        'style': '悬疑紧张'
    }
    
    try:
        response = requests.post('http://localhost:5000/api/plot-development', json=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                suggestions = result.get('data', {}).get('suggestions', '')
                print("✅ 情节发展建议生成成功")
                print(f"📝 内容长度: {len(suggestions)}")
                print(f"📄 内容预览:\n{suggestions[:300]}...")
                return True
            else:
                print(f"❌ 生成失败: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_consultation():
    """测试写作咨询功能"""
    print("\n❓ 测试写作咨询功能...")
    
    data = {
        'question': '我在写一个悬疑小说，但总觉得悬念设置得不够好，读者很容易就猜到结局。请问有什么技巧可以让悬念更有效？',
        'context': '正在创作第一部悬疑小说，有一定的写作基础',
        'type': '技巧指导'
    }
    
    try:
        response = requests.post('http://localhost:5000/api/consultation', json=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                answer = result.get('data', {}).get('answer', '')
                print("✅ 写作咨询回答生成成功")
                print(f"📝 内容长度: {len(answer)}")
                print(f"📄 内容预览:\n{answer[:300]}...")
                return True
            else:
                print(f"❌ 咨询失败: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_ui_access():
    """测试UI访问"""
    print("\n🌐 测试UI访问...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=10)
        if response.status_code == 200:
            content = response.text
            # 检查是否包含关键元素
            if '莎莎助手' in content and '情节发展' in content and '写作咨询' in content:
                if '类型指南' not in content:
                    print("✅ UI访问正常，类型指南已成功移除")
                    return True
                else:
                    print("⚠️ UI访问正常，但类型指南仍然存在")
                    return False
            else:
                print("⚠️ UI访问正常，但缺少关键功能")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 测试优化后的功能")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    tests = [
        ("UI界面检查", test_ui_access),
        ("情节发展功能", test_plot_development),
        ("写作咨询功能", test_consultation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试未通过")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
        
        # 测试间隔
        time.sleep(2)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有优化都成功实现！")
        print("\n✅ 完成的优化:")
        print("  - 移除了类型指南功能")
        print("  - 优化了情节发展输出格式")
        print("  - 优化了写作咨询输出格式")
        print("  - 改进了CSS样式，提升视觉效果")
    else:
        print("⚠️ 部分优化可能存在问题，请检查相关功能")
    
    print("\n💡 提示:")
    print("- 现在可以在浏览器中访问 http://localhost:5000")
    print("- 尝试使用情节发展和写作咨询功能，查看新的输出格式")
    print("- 确认类型指南功能已从界面中移除")

if __name__ == '__main__':
    main()
