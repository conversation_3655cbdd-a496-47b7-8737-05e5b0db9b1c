"""
知识库管理器
整合数据加载、文本处理和向量存储功能
"""
from typing import List, Dict, Any
from .data_loader import NovelDataLoader
from .text_processor import TextProcessor
from .vector_store import SimpleVectorStore

class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self):
        self.data_loader = NovelDataLoader()
        self.text_processor = TextProcessor()
        self.vector_store = SimpleVectorStore()
        self.is_initialized = False
    
    def initialize(self, force_reload: bool = False):
        """
        初始化知识库
        """
        print("开始初始化知识库...")
        
        # 尝试加载现有索引
        if not force_reload:
            self.vector_store.load_index()
            if self.vector_store.documents:
                self.is_initialized = True
                print("知识库初始化完成（使用现有索引）")
                return
        
        # 加载原始数据
        print("加载小说数据...")
        raw_documents = self.data_loader.load_from_modelscope()
        
        if not raw_documents:
            print("未能加载到数据，知识库初始化失败")
            return
        
        # 处理文本数据
        print("处理文本数据...")
        processed_chunks = self.text_processor.process_documents(raw_documents)
        
        # 添加到向量存储
        print("构建向量索引...")
        self.vector_store.clear()  # 清空现有数据
        self.vector_store.add_documents(processed_chunks)
        
        # 保存索引
        self.vector_store.save_index()
        
        self.is_initialized = True
        print("知识库初始化完成")
        
        # 打印统计信息
        stats = self.get_statistics()
        print(f"知识库统计: {stats}")
    
    def search_knowledge(self, query: str, top_k: int = 5, genre_filter: str = None) -> List[Dict[str, Any]]:
        """
        搜索知识库
        """
        if not self.is_initialized:
            print("知识库未初始化，正在初始化...")
            self.initialize()
        
        return self.vector_store.search(query, top_k, genre_filter)
    
    def get_writing_inspiration(self, genre: str = None, count: int = 3) -> List[Dict[str, Any]]:
        """
        获取写作灵感
        """
        if not self.is_initialized:
            self.initialize()
        
        if genre:
            return self.vector_store.search_by_genre(genre, count)
        else:
            return self.vector_store.get_random_samples(count)
    
    def get_genre_examples(self, genre: str, count: int = 5) -> List[Dict[str, Any]]:
        """
        获取特定类型的示例
        """
        if not self.is_initialized:
            self.initialize()
        
        return self.vector_store.search_by_genre(genre, count)
    
    def get_author_works(self, author: str, count: int = 5) -> List[Dict[str, Any]]:
        """
        获取特定作者的作品
        """
        if not self.is_initialized:
            self.initialize()
        
        return self.vector_store.search_by_author(author, count)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        """
        if not self.is_initialized:
            return {'status': 'not_initialized'}
        
        return self.vector_store.get_statistics()
    
    def get_available_genres(self) -> List[str]:
        """
        获取可用的小说类型
        """
        if not self.is_initialized:
            self.initialize()
        
        stats = self.vector_store.get_statistics()
        return list(stats.get('genre_distribution', {}).keys())
    
    def get_available_authors(self) -> List[str]:
        """
        获取可用的作者列表
        """
        if not self.is_initialized:
            self.initialize()
        
        stats = self.vector_store.get_statistics()
        return list(stats.get('author_distribution', {}).keys())
    
    def reload_knowledge_base(self):
        """
        重新加载知识库
        """
        print("重新加载知识库...")
        self.is_initialized = False
        self.initialize(force_reload=True)
    
    def add_custom_content(self, title: str, content: str, author: str = "用户", genre: str = "自定义"):
        """
        添加自定义内容到知识库
        """
        if not self.is_initialized:
            self.initialize()
        
        # 创建文档
        document = {
            'title': title,
            'content': content,
            'author': author,
            'genre': genre,
            'summary': content[:200] + '...' if len(content) > 200 else content
        }
        
        # 处理文档
        processed_chunks = self.text_processor.process_documents([document])
        
        # 添加到向量存储
        self.vector_store.add_documents(processed_chunks)
        
        # 保存索引
        self.vector_store.save_index()
        
        print(f"已添加自定义内容: {title}")
    
    def export_knowledge_summary(self) -> Dict[str, Any]:
        """
        导出知识库摘要
        """
        if not self.is_initialized:
            self.initialize()
        
        stats = self.get_statistics()
        genres = self.get_available_genres()
        authors = self.get_available_authors()
        
        # 获取每个类型的示例
        genre_examples = {}
        for genre in genres[:5]:  # 只取前5个类型
            examples = self.get_genre_examples(genre, 2)
            genre_examples[genre] = [
                {
                    'title': ex.get('title', ''),
                    'content_preview': ex.get('content', '')[:100] + '...'
                }
                for ex in examples
            ]
        
        return {
            'statistics': stats,
            'available_genres': genres,
            'available_authors': authors[:10],  # 只返回前10个作者
            'genre_examples': genre_examples
        }
