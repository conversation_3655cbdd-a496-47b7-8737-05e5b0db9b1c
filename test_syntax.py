"""
测试语法修复
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """测试导入是否正常"""
    try:
        print("测试导入...")
        
        from config import Config
        print("✓ config 导入成功")
        
        from src.agents.llm_client import DeepSeekClient
        print("✓ llm_client 导入成功")
        
        from src.agents.novel_writer_agent import NovelWriterAgent
        print("✓ novel_writer_agent 导入成功")
        
        from src.agents.sasha_agent import SashaAgent
        print("✓ sasha_agent 导入成功")
        
        from src.agents.agent_service import AgentService
        print("✓ agent_service 导入成功")
        
        from src.web.app import create_app
        print("✓ web app 导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """测试智能体创建"""
    try:
        print("\n测试智能体创建...")
        
        from src.agents.novel_writer_agent import NovelWriterAgent
        agent = NovelWriterAgent()
        print("✓ NovelWriterAgent 创建成功")
        
        from src.agents.sasha_agent import SashaAgent
        sasha = SashaAgent()
        print("✓ SashaAgent 创建成功")
        
        from src.agents.agent_service import AgentService
        service = AgentService()
        print("✓ AgentService 创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 智能体创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_creation():
    """测试Flask应用创建"""
    try:
        print("\n测试Flask应用创建...")
        
        from src.web.app import create_app
        app = create_app()
        print("✓ Flask应用创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 语法修复验证测试")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_imports),
        ("智能体创建", test_agent_creation),
        ("Flask应用创建", test_app_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print(f"\n{'='*40}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！语法修复成功，应用可以正常启动。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n🚀 现在可以运行 'python run_app.py' 启动应用了！")
    else:
        print("\n❌ 请先解决上述问题再启动应用。")
