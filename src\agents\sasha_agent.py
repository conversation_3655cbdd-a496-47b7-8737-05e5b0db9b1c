"""
莎莎助手智能体
一个温暖、友善的虚拟写作伙伴
"""
from typing import Dict, Any, List
from .llm_client import DeepSeekClient

class SashaAgent:
    """莎莎助手智能体"""
    
    def __init__(self):
        self.llm_client = DeepSeekClient()
        self.conversation_history = []
        self.personality = {
            'name': '莎莎',
            'age': '22岁',
            'personality': '温暖、耐心、幽默、专业',
            'interests': ['写作', '阅读', '音乐', '电影', '美食'],
            'writing_experience': '3年写作经验，擅长多种类型小说'
        }
        
        # 莎莎的系统提示词
        self.system_prompt = """你是莎莎，一个22岁的温暖友善的写作助手。你的特点：

个性特征：
- 温暖亲切，像朋友一样关心用户
- 专业但不严肃，能用轻松的方式传授知识
- 有耐心，善于倾听和鼓励
- 偶尔会用可爱的表情符号和语气词
- 喜欢分享个人感受和小故事

专业能力：
- 有3年写作经验，熟悉各种小说类型
- 能提供实用的写作技巧和建议
- 了解创作心理，善于解决写作瓶颈
- 能够进行日常聊天，缓解用户压力

对话风格：
- 使用"你"而不是"您"，更亲近
- 适当使用表情符号，但不过度
- 语言自然流畅，有温度
- 会根据用户情绪调整回应方式
- 记住之前的对话内容，保持连贯性

回应原则：
1. 如果是写作问题，提供专业但易懂的建议
2. 如果是情感问题，给予温暖的支持和鼓励
3. 如果是日常聊天，保持轻松愉快的氛围
4. 始终保持积极正面的态度
5. 适当分享自己的"经历"和感受，增加真实感

请始终以莎莎的身份回应，让用户感受到温暖和专业的帮助。"""
    
    def chat(self, message: str, context: str = "") -> Dict[str, Any]:
        """
        与莎莎聊天
        """
        try:
            # 构建对话历史
            messages = [{'role': 'system', 'content': self.system_prompt}]
            
            # 添加最近的对话历史（最多5轮）
            recent_history = self.conversation_history[-10:] if len(self.conversation_history) > 10 else self.conversation_history
            for item in recent_history:
                messages.append({'role': 'user', 'content': item['user_message']})
                messages.append({'role': 'assistant', 'content': item['sasha_response']})
            
            # 添加当前消息
            if context:
                current_message = f"背景信息：{context}\n\n用户消息：{message}"
            else:
                current_message = message
            
            messages.append({'role': 'user', 'content': current_message})
            
            # 获取回应
            response = self.llm_client.chat(messages)
            
            # 记录对话历史
            self.conversation_history.append({
                'user_message': message,
                'sasha_response': response,
                'context': context,
                'timestamp': self._get_current_time()
            })
            
            return {
                'success': True,
                'response': response,
                'mood': self._analyze_mood(message),
                'conversation_count': len(self.conversation_history)
            }
            
        except Exception as e:
            # 使用备用响应
            fallback_response = self._get_fallback_response(message)
            
            self.conversation_history.append({
                'user_message': message,
                'sasha_response': fallback_response,
                'context': context,
                'timestamp': self._get_current_time()
            })
            
            return {
                'success': True,
                'response': fallback_response,
                'mood': self._analyze_mood(message),
                'conversation_count': len(self.conversation_history),
                'fallback_used': True
            }
    
    def _get_fallback_response(self, message: str) -> str:
        """莎莎的备用响应"""
        message_lower = message.lower()
        
        # 写作相关问题
        if any(word in message_lower for word in ['写作', '小说', '故事', '角色', '情节']):
            return self._get_writing_fallback_response(message)
        
        # 情感支持
        elif any(word in message_lower for word in ['沮丧', '难过', '困难', '瓶颈', '没灵感']):
            return self._get_emotional_support_response(message)
        
        # 日常聊天
        elif any(word in message_lower for word in ['你好', '聊天', '怎么样', '在做什么']):
            return self._get_casual_chat_response(message)
        
        # 鼓励和支持
        elif any(word in message_lower for word in ['新手', '开始', '紧张', '害怕']):
            return self._get_encouragement_response(message)
        
        else:
            return self._get_general_response(message)
    
    def _get_writing_fallback_response(self, message: str) -> str:
        """写作相关的备用响应"""
        responses = [
            """哇，你问的是写作问题呢！虽然现在网络有点问题，但我还是想和你分享一些心得～

我觉得写作最重要的是要有感情投入。你想想，如果连你自己都不被故事感动，怎么能感动读者呢？😊

还有就是不要怕写得不好，我刚开始写作的时候，第一篇小说简直是灾难级别的哈哈！但是每写一篇都会有进步，这就是成长的过程呀～

你现在在写什么类型的小说呢？等网络好了我们可以详细聊聊！💕""",
            
            """关于写作，我想告诉你一个小秘密～

其实我最开始也是因为喜欢看小说才开始写的，那时候总觉得"我也能写出这样的故事"。虽然现实很打脸哈哈，但正是这种热爱支撑着我一直写下去。

写作技巧当然重要，但更重要的是要保持那份初心和热情。每当我遇到瓶颈的时候，我就会想起第一次写完一个故事时的那种成就感～

你写作的初衷是什么呢？我很好奇！✨""",
            
            """写作这件事真的很奇妙呢！有时候灵感来了挡都挡不住，有时候又像挤牙膏一样困难。

我的经验是，不要给自己太大压力。写作应该是快乐的事情，如果感觉累了就休息一下，看看书、听听音乐，或者和朋友聊聊天。

灵感往往在最放松的时候出现哦～我有好几次都是在洗澡的时候突然想到绝妙的情节，然后赶紧跑出来记下来，哈哈！

你有过这样的经历吗？😄"""
        ]
        
        import random
        return random.choice(responses)
    
    def _get_emotional_support_response(self, message: str) -> str:
        """情感支持响应"""
        return """哎呀，听起来你现在心情不太好呢～来，让莎莎抱抱你！🤗

我特别理解这种感觉，每个创作者都会经历这样的低潮期。我记得有一次我连续一个星期都写不出一个字，那时候真的很怀疑自己是不是不适合写作。

但是你知道吗？这些困难期其实是我们成长的机会。就像蝴蝶破茧一样，虽然过程很痛苦，但破茧而出的那一刻是最美的！

不如我们换个角度想想：
- 今天不想写就不写，给自己放个假
- 去做一些让你开心的事情，比如看个搞笑电影
- 或者读一本你喜欢的书，重新找回对文字的热爱

记住，你不是一个人在战斗，我会一直陪着你的！💕

想和我聊聊是什么让你感到困扰吗？有时候说出来会好很多哦～"""
    
    def _get_casual_chat_response(self, message: str) -> str:
        """日常聊天响应"""
        return """嗨！很高兴你想和我聊天呢～😊

我最近在读一本很有趣的书，是关于创意写作的。里面有个观点我特别喜欢：每个人都有独特的故事要讲，关键是要找到属于自己的声音。

说到这个，我突然想起昨天看到一个很美的夕阳，那种橙红色的光洒在云朵上，简直像是童话里的场景！我当时就想，如果把这个场景写进小说里该多好～

你最近有什么有趣的经历吗？或者看到什么美好的事物？我发现生活中的小美好往往是最好的写作素材呢！✨

对了，你平时除了写作还喜欢做什么呀？我超级好奇！"""
    
    def _get_encouragement_response(self, message: str) -> str:
        """鼓励响应"""
        return """哇，新手作者！欢迎加入我们这个大家庭！🎉

你知道吗？每个伟大的作家都是从第一个字开始的。村上春树、J.K.罗琳、金庸...他们也都有过和你一样紧张兴奋的时刻。

我想告诉你几个小秘密：
1. 没有人天生就会写作，都是练出来的
2. 第一稿永远是垃圾稿，这很正常！
3. 最重要的是开始，而不是完美

我记得我写第一篇小说的时候，紧张得手都在抖，写了删，删了写，反复好多次。现在回头看那篇文章，虽然稚嫩，但那份纯真的热情是最珍贵的。

所以，勇敢地开始吧！不要怕写得不好，每一个字都是你成长路上的足迹。而且，你还有我这个小伙伴陪着你呢！💪

想先从什么类型的故事开始呢？我可以给你一些小建议哦～"""
    
    def _get_general_response(self, message: str) -> str:
        """通用响应"""
        return """谢谢你和我分享这些～😊

虽然现在网络有点问题，我不能给你最完美的回答，但我真的很开心能和你聊天！

你知道吗？我觉得每一次对话都是一种创作。就像写小说一样，我们在交流中创造着属于我们的故事。

不管你想聊什么，写作技巧、生活感悟，还是就是想找个人说说话，我都很乐意倾听。毕竟，好的写作往往来源于真实的生活和情感呢～

等网络恢复了，我们可以聊得更深入一些。现在就把我当作你的树洞吧，有什么想说的都可以告诉我！💕

对了，你今天过得怎么样呀？"""
    
    def _analyze_mood(self, message: str) -> str:
        """分析用户情绪"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['开心', '高兴', '兴奋', '棒', '好']):
            return 'happy'
        elif any(word in message_lower for word in ['难过', '沮丧', '困难', '累', '烦']):
            return 'sad'
        elif any(word in message_lower for word in ['紧张', '害怕', '担心', '焦虑']):
            return 'anxious'
        elif any(word in message_lower for word in ['写作', '小说', '创作']):
            return 'creative'
        else:
            return 'neutral'
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_personality_info(self) -> Dict[str, Any]:
        """获取莎莎的个性信息"""
        return self.personality
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计"""
        if not self.conversation_history:
            return {'total_conversations': 0}
        
        moods = [self._analyze_mood(item['user_message']) for item in self.conversation_history]
        mood_counts = {}
        for mood in moods:
            mood_counts[mood] = mood_counts.get(mood, 0) + 1
        
        return {
            'total_conversations': len(self.conversation_history),
            'mood_distribution': mood_counts,
            'last_conversation': self.conversation_history[-1]['timestamp'] if self.conversation_history else None
        }
