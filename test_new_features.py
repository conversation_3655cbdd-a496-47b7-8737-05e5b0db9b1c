"""
测试新功能的脚本
"""
import sys
import os
import requests
import json
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

def test_api_endpoint(url, method='GET', data=None):
    """测试API端点"""
    try:
        if method == 'GET':
            response = requests.get(url, timeout=15)
        else:
            response = requests.post(url, json=data, timeout=90)  # 增加超时时间
        
        if response.status_code == 200:
            result = response.json()
            return result.get('success', False), result
        else:
            return False, f"HTTP {response.status_code}: {response.text}"
    except Exception as e:
        return False, str(e)

def test_story_idea():
    """测试故事创意生成"""
    print("\n🎯 测试故事创意生成...")
    
    data = {
        'genre': '现代都市',
        'theme': '友情与成长',
        'length': '中篇'
    }
    
    success, result = test_api_endpoint('http://localhost:5000/api/story-idea', 'POST', data)
    
    if success:
        print("✅ 故事创意生成成功")
        print(f"📝 生成内容长度: {len(result.get('data', {}).get('story_idea', ''))}")
        return True
    else:
        print(f"❌ 故事创意生成失败: {result}")
        return False

def test_character_development():
    """测试角色开发"""
    print("\n👤 测试角色开发...")
    
    data = {
        'character_type': '主角',
        'genre': '玄幻修仙',
        'background': '出身平凡的少年，意外获得修仙机缘'
    }
    
    success, result = test_api_endpoint('http://localhost:5000/api/character', 'POST', data)
    
    if success:
        print("✅ 角色开发成功")
        print(f"📝 生成内容长度: {len(result.get('data', {}).get('character_profile', ''))}")
        return True
    else:
        print(f"❌ 角色开发失败: {result}")
        return False

def test_writing_improvement():
    """测试写作改进"""
    print("\n✏️ 测试写作改进...")
    
    data = {
        'text': '他走在路上，心情很复杂。今天发生的事情让他很困惑。',
        'improvement_type': '文笔'
    }
    
    success, result = test_api_endpoint('http://localhost:5000/api/improve-writing', 'POST', data)
    
    if success:
        print("✅ 写作改进成功")
        print(f"📝 生成内容长度: {len(result.get('data', {}).get('improved_version', ''))}")
        return True
    else:
        print(f"❌ 写作改进失败: {result}")
        return False

def test_plot_development():
    """测试情节发展"""
    print("\n🗺️ 测试情节发展...")
    
    data = {
        'current_plot': '主角刚刚发现了一个重大秘密，但还不知道该如何处理。',
        'direction': '增加冲突',
        'style': '悬疑紧张'
    }
    
    success, result = test_api_endpoint('http://localhost:5000/api/plot-development', 'POST', data)
    
    if success:
        print("✅ 情节发展成功")
        print(f"📝 生成内容长度: {len(result.get('data', {}).get('suggestions', ''))}")
        return True
    else:
        print(f"❌ 情节发展失败: {result}")
        return False

def test_consultation():
    """测试写作咨询"""
    print("\n❓ 测试写作咨询...")
    
    data = {
        'question': '我是新手作者，应该如何开始写小说？',
        'context': '完全没有写作经验',
        'type': '技巧指导'
    }
    
    success, result = test_api_endpoint('http://localhost:5000/api/consultation', 'POST', data)
    
    if success:
        print("✅ 写作咨询成功")
        print(f"📝 生成内容长度: {len(result.get('data', {}).get('answer', ''))}")
        return True
    else:
        print(f"❌ 写作咨询失败: {result}")
        return False

def test_sasha_chat():
    """测试莎莎聊天"""
    print("\n💕 测试莎莎聊天...")
    
    data = {
        'message': '嗨莎莎，我今天心情不太好，你能安慰一下我吗？'
    }
    
    success, result = test_api_endpoint('http://localhost:5000/api/sasha-chat', 'POST', data)
    
    if success:
        print("✅ 莎莎聊天成功")
        print(f"📝 回复内容长度: {len(result.get('data', {}).get('response', ''))}")
        print(f"😊 检测到的情绪: {result.get('data', {}).get('mood', 'unknown')}")
        return True
    else:
        print(f"❌ 莎莎聊天失败: {result}")
        return False



def test_system_status():
    """测试系统状态"""
    print("\n🔧 测试系统状态...")
    
    success, result = test_api_endpoint('http://localhost:5000/api/status')
    
    if success:
        status = result.get('data', {})
        print("✅ 系统状态获取成功")
        print(f"📊 初始化状态: {status.get('initialized')}")
        print(f"📚 知识库状态: {status.get('knowledge_base_ready')}")
        print(f"🤖 LLM连接状态: {status.get('llm_connection')}")
        return True
    else:
        print(f"❌ 系统状态获取失败: {result}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试小说写作专家的新功能")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    tests = [
        ("系统状态", test_system_status),
        ("故事创意生成", test_story_idea),
        ("角色开发", test_character_development),
        ("写作改进", test_writing_improvement),
        ("情节发展", test_plot_development),
        ("写作咨询", test_consultation),
        ("莎莎聊天", test_sasha_chat)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  {test_name} 测试未通过")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
        
        # 测试间隔
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！系统运行正常。")
    elif passed >= total * 0.7:
        print("✅ 大部分功能正常，系统基本可用。")
    else:
        print("⚠️  多个功能存在问题，请检查系统配置。")
    
    print("\n💡 提示：")
    print("- 如果API连接失败，请检查DeepSeek API密钥和网络连接")
    print("- 如果功能异常，请查看控制台日志获取详细错误信息")
    print("- 备用响应机制会在API不可用时自动启用")

if __name__ == '__main__':
    main()
