# 小说写作专家 - 基于LangChain的智能写作助手

## 项目简介

这是一个基于LangChain和DeepSeek API的智能小说写作助手，旨在帮助作者进行小说创作。系统集成了知识库检索、智能对话和Web界面，提供全方位的写作支持。

## 功能特性

### 🎯 核心功能
- **故事创意生成**: 根据类型、主题生成独特的故事创意
- **角色开发**: 创造立体生动的角色形象和背景设定
- **写作改进**: 提供专业的文笔优化和内容改进建议
- **情节发展**: 协助规划故事情节和发展方向
- **写作咨询**: 解答各种写作相关问题
- **类型指南**: 提供不同小说类型的写作指导

### 🔧 技术特性
- 基于LangChain框架的智能体架构
- 集成DeepSeek大语言模型
- 支持ModelScope小说数据集
- 简化的向量检索系统
- 响应式Web界面
- RESTful API接口

## 项目结构

```
langchain_gem/
├── config.py                 # 配置文件
├── main.py                   # 主启动文件
├── run_app.py               # 简化启动脚本
├── test_app.py              # 测试脚本
├── requirements.txt         # 依赖包列表
├── requirements_basic.txt   # 基础依赖包
├── .env                     # 环境变量配置
├── src/                     # 源代码目录
│   ├── agents/              # 智能体模块
│   │   ├── llm_client.py    # LLM客户端
│   │   ├── novel_writer_agent.py  # 小说写作智能体
│   │   └── agent_service.py # 智能体服务层
│   ├── knowledge/           # 知识库模块
│   │   ├── data_loader.py   # 数据加载器
│   │   ├── text_processor.py # 文本处理器
│   │   ├── vector_store.py  # 向量存储
│   │   └── knowledge_manager.py # 知识库管理器
│   └── web/                 # Web应用模块
│       ├── app.py           # Flask应用
│       ├── templates/       # HTML模板
│       │   ├── base.html
│       │   ├── index.html
│       │   └── error.html
│       └── static/          # 静态资源
│           ├── css/style.css
│           └── js/main.js
└── chroma_db/               # 向量数据库存储
```

## 安装与配置

### 1. 环境要求
- Python 3.8+
- 建议使用conda环境

### 2. 安装依赖
```bash
# 安装基础依赖
pip install -r requirements_basic.txt

# 安装中文分词库
pip install jieba

# 如果网络允许，可以安装完整依赖
pip install -r requirements.txt
```

### 3. 配置环境变量
编辑 `.env` 文件，设置以下配置：
```
# DeepSeek API配置
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 知识库路径
KNOWLEDGE_BASE_PATH=D:\\gitbase\\xiaoshuo

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=5000
```

### 4. 知识库设置
- 将小说文本文件放置在 `KNOWLEDGE_BASE_PATH` 指定的目录中
- 支持 `.txt`, `.json`, `.md` 格式的文件
- 系统会自动处理和索引这些文件

## 使用方法

### 1. 启动应用
```bash
# 使用主启动文件
python main.py

# 或使用简化启动脚本
python run_app.py
```

### 2. 访问Web界面
打开浏览器访问: `http://localhost:5000`

### 3. 运行测试
```bash
python test_app.py
```

## API接口

### 系统状态
- `GET /api/status` - 获取系统状态
- `GET /api/genres` - 获取可用的小说类型
- `GET /api/knowledge-stats` - 获取知识库统计信息

### 写作功能
- `POST /api/story-idea` - 生成故事创意
- `POST /api/character` - 开发角色
- `POST /api/improve-writing` - 改进写作
- `POST /api/plot-development` - 情节发展建议
- `POST /api/consultation` - 写作咨询
- `POST /api/genre-guide` - 获取类型写作指南

### 对话管理
- `GET /api/history` - 获取对话历史
- `POST /api/clear-history` - 清空对话历史

## 技术架构

### 智能体架构
- **LLM客户端**: 封装DeepSeek API调用
- **写作专家智能体**: 核心业务逻辑和提示词工程
- **服务层**: 统一的API接口和错误处理

### 知识库系统
- **数据加载器**: 支持ModelScope和本地文件加载
- **文本处理器**: 中文分词、文本清理和分块
- **向量存储**: 简化的文本检索和相似度计算
- **知识库管理器**: 统一的知识库操作接口

### Web应用
- **Flask后端**: RESTful API和模板渲染
- **响应式前端**: Bootstrap + jQuery
- **实时交互**: AJAX异步请求

## 特色功能

### 1. 智能故事创意生成
- 支持多种小说类型
- 结合知识库内容生成创意
- 提供详细的故事梗概和人物设定

### 2. 专业角色开发
- 多维度角色塑造
- 背景故事生成
- 角色关系网络构建

### 3. 写作改进建议
- 文笔优化
- 情节完善
- 对话改进
- 描写增强

### 4. 知识库增强
- 自动文本处理和索引
- 智能内容检索
- 上下文相关推荐

## 开发说明

### 扩展功能
1. 在 `src/agents/` 中添加新的智能体
2. 在 `src/web/app.py` 中添加新的API端点
3. 在前端添加对应的界面和交互

### 自定义配置
- 修改 `config.py` 中的配置参数
- 调整提示词模板
- 优化文本处理逻辑

## 注意事项

1. **API密钥安全**: 请妥善保管DeepSeek API密钥
2. **网络连接**: 确保能够访问DeepSeek API服务
3. **知识库路径**: 确保知识库路径存在且有读取权限
4. **依赖安装**: 如遇网络问题，可先安装基础依赖

## 故障排除

### 常见问题
1. **模块导入错误**: 检查Python路径和依赖安装
2. **API连接失败**: 检查网络连接和API密钥
3. **知识库加载失败**: 检查文件路径和权限
4. **Web界面无法访问**: 检查端口占用和防火墙设置

### 调试方法
1. 运行 `python test_app.py` 进行系统测试
2. 检查控制台输出的错误信息
3. 查看Flask应用的调试日志

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 许可证

本项目采用MIT许可证。

---

**小说写作专家** - 让AI成为您的创作伙伴！ 🎨✍️
