"""
小说写作专家智能体
"""
from typing import Dict, Any, List
from .llm_client import DeepSeekClient
from ..knowledge.knowledge_manager import KnowledgeManager

class NovelWriterAgent:
    """小说写作专家智能体"""
    
    def __init__(self):
        self.llm_client = DeepSeekClient()
        self.knowledge_manager = KnowledgeManager()
        self.conversation_history = []
        
        # 初始化知识库
        self.knowledge_manager.initialize()
        
        # 系统提示词
        self.system_prompt = """你是一位资深的小说写作专家，拥有丰富的创作经验和深厚的文学功底。你的专长包括：

1. **故事构思**：帮助作者构思引人入胜的故事情节
2. **人物塑造**：创造立体、生动的角色形象
3. **文笔指导**：提供写作技巧和文笔改进建议
4. **类型专精**：熟悉各种小说类型的写作特点
5. **结构规划**：协助规划小说的整体结构和章节安排

你的回答应该：
- 专业且实用，提供具体可操作的建议
- 结合具体例子说明写作技巧
- 根据用户需求提供个性化指导
- 保持鼓励和支持的态度
- 必要时引用相关的优秀作品作为参考

请始终以专业、友善的态度帮助用户提升写作水平。"""
    
    def get_knowledge_context(self, query: str, genre: str = None) -> str:
        """
        从知识库获取相关上下文
        """
        try:
            # 搜索相关内容
            relevant_docs = self.knowledge_manager.search_knowledge(query, top_k=3, genre_filter=genre)
            
            if not relevant_docs:
                return ""
            
            context_parts = []
            for doc in relevant_docs:
                title = doc.get('title', '')
                content = doc.get('content', '')[:300]  # 限制长度
                author = doc.get('author', '')
                genre_info = doc.get('genre', '')
                
                context_parts.append(f"参考作品：《{title}》（{author}，{genre_info}）\n内容片段：{content}...")
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            print(f"获取知识库上下文失败: {e}")
            return ""
    
    def generate_story_idea(self, genre: str, theme: str = "", length: str = "中篇") -> Dict[str, Any]:
        """
        生成故事创意
        """
        # 获取该类型的参考作品
        context = self.get_knowledge_context(f"{genre} {theme}", genre)
        
        prompt = f"""请为我生成一个{genre}小说的创意，要求：
1. 类型：{genre}
2. 主题：{theme if theme else '自由发挥'}
3. 长度：{length}

请提供：
- 故事梗概
- 主要人物设定
- 核心冲突
- 故事亮点
- 目标读者群体

{f'参考资料：{context}' if context else ''}"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'genre': genre,
            'theme': theme,
            'length': length,
            'story_idea': response,
            'context_used': bool(context)
        }
    
    def develop_character(self, character_type: str, genre: str = "", background: str = "") -> Dict[str, Any]:
        """
        开发角色
        """
        context = self.get_knowledge_context(f"{character_type} {genre}", genre)
        
        prompt = f"""请帮我设计一个{character_type}角色，要求：
1. 小说类型：{genre if genre else '通用'}
2. 背景设定：{background if background else '自由发挥'}

请详细描述：
- 基本信息（姓名、年龄、职业等）
- 外貌特征
- 性格特点
- 背景故事
- 核心动机和目标
- 角色弧线（成长变化）
- 与其他角色的关系

{f'参考资料：{context}' if context else ''}"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'character_type': character_type,
            'genre': genre,
            'character_profile': response,
            'context_used': bool(context)
        }
    
    def improve_writing(self, text: str, improvement_type: str = "综合") -> Dict[str, Any]:
        """
        改进写作
        """
        prompt = f"""请帮我改进以下文本，重点关注{improvement_type}：

原文：
{text}

请提供：
1. 具体的改进建议
2. 修改后的版本
3. 改进要点说明
4. 写作技巧提醒"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'original_text': text,
            'improvement_type': improvement_type,
            'improved_version': response
        }
    
    def plot_development(self, current_plot: str, direction: str = "") -> Dict[str, Any]:
        """
        情节发展建议
        """
        prompt = f"""基于当前的情节发展，请提供后续剧情建议：

当前情节：
{current_plot}

发展方向：{direction if direction else '请自由发挥'}

请提供：
1. 3-5个可能的发展方向
2. 每个方向的优缺点分析
3. 推荐的发展路径
4. 需要注意的写作要点"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'current_plot': current_plot,
            'direction': direction,
            'suggestions': response
        }
    
    def writing_consultation(self, question: str, context: str = "") -> Dict[str, Any]:
        """
        写作咨询
        """
        # 尝试从知识库获取相关信息
        knowledge_context = self.get_knowledge_context(question)
        
        full_context = ""
        if context:
            full_context += f"用户背景：{context}\n\n"
        if knowledge_context:
            full_context += f"参考资料：{knowledge_context}\n\n"
        
        prompt = f"""{full_context}用户问题：{question}

请作为专业的小说写作专家，提供详细、实用的建议。"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        # 记录对话历史
        self.conversation_history.append({
            'question': question,
            'context': context,
            'response': response
        })
        
        return {
            'question': question,
            'answer': response,
            'knowledge_used': bool(knowledge_context)
        }
    
    def get_genre_guide(self, genre: str) -> Dict[str, Any]:
        """
        获取类型写作指南
        """
        # 获取该类型的示例作品
        examples = self.knowledge_manager.get_genre_examples(genre, 3)
        
        context = ""
        if examples:
            context = f"该类型的优秀作品示例：\n"
            for ex in examples:
                context += f"- 《{ex.get('title', '')}》：{ex.get('content', '')[:100]}...\n"
        
        prompt = f"""请提供{genre}小说的详细写作指南，包括：

1. 类型特点和核心要素
2. 常见的情节模式
3. 人物设定要点
4. 写作技巧和注意事项
5. 读者期待和市场特点
6. 成功案例分析

{context}"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'genre': genre,
            'guide': response,
            'examples_count': len(examples)
        }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        获取对话历史
        """
        return self.conversation_history
    
    def clear_history(self):
        """
        清空对话历史
        """
        self.conversation_history = []
