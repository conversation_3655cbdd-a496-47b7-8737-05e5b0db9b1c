"""
小说写作专家智能体
"""
from typing import Dict, Any, List
from .llm_client import DeepSeekClient
from ..knowledge.knowledge_manager import KnowledgeManager

class NovelWriterAgent:
    """小说写作专家智能体"""
    
    def __init__(self):
        self.llm_client = DeepSeekClient()
        self.knowledge_manager = KnowledgeManager()
        self.conversation_history = []
        
        # 初始化知识库
        self.knowledge_manager.initialize()
        
        # 系统提示词
        self.system_prompt = """你是一位资深的小说写作专家，拥有丰富的创作经验和深厚的文学功底。你的专长包括：

1. **故事构思**：帮助作者构思引人入胜的故事情节
2. **人物塑造**：创造立体、生动的角色形象
3. **文笔指导**：提供写作技巧和文笔改进建议
4. **类型专精**：熟悉各种小说类型的写作特点
5. **结构规划**：协助规划小说的整体结构和章节安排

你的回答应该：
- 专业且实用，提供具体可操作的建议
- 结合具体例子说明写作技巧
- 根据用户需求提供个性化指导
- 保持鼓励和支持的态度
- 必要时引用相关的优秀作品作为参考

请始终以专业、友善的态度帮助用户提升写作水平。"""
    
    def get_knowledge_context(self, query: str, genre: str = None) -> str:
        """
        从知识库获取相关上下文
        """
        try:
            # 搜索相关内容
            relevant_docs = self.knowledge_manager.search_knowledge(query, top_k=3, genre_filter=genre)
            
            if not relevant_docs:
                return ""
            
            context_parts = []
            for doc in relevant_docs:
                title = doc.get('title', '')
                content = doc.get('content', '')[:300]  # 限制长度
                author = doc.get('author', '')
                genre_info = doc.get('genre', '')
                
                context_parts.append(f"参考作品：《{title}》（{author}，{genre_info}）\n内容片段：{content}...")
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            print(f"获取知识库上下文失败: {e}")
            return ""
    
    def generate_story_idea(self, genre: str, theme: str = "", length: str = "中篇") -> Dict[str, Any]:
        """
        生成故事创意
        """
        # 获取该类型的参考作品
        context = self.get_knowledge_context(f"{genre} {theme}", genre)
        
        context_section = f'📖 **参考资料分析**\n{context}\n' if context else ''

        prompt = f"""作为专业的小说创作顾问，请为我生成一个{genre}小说的详细创意方案：

📋 **创作需求**
- 小说类型：{genre}
- 主题方向：{theme if theme else '开放式主题，发挥创意'}
- 预期长度：{length}

📝 **请按以下格式提供完整的创意方案**

## 📖 故事梗概
（200-300字的故事核心概述，包含主要情节线）

## 👥 主要人物设定
**主角：**
- 姓名、年龄、职业
- 核心性格特征
- 主要目标和动机

**重要配角：**
- 至少2-3个关键角色
- 各自的作用和关系

## ⚔️ 核心冲突
- 外在冲突：（主角面临的外部挑战）
- 内在冲突：（主角的心理斗争）
- 冲突升级路径

## ✨ 故事亮点
- 独特卖点（3-5个）
- 情感共鸣点
- 创新元素

## 🎯 目标读者
- 年龄群体
- 兴趣偏好
- 阅读习惯

## 📚 创作建议
- 写作重点
- 需要注意的问题
- 推荐的叙事技巧

{context_section}

请确保创意具有可操作性和吸引力，适合{length}的篇幅规划。"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'genre': genre,
            'theme': theme,
            'length': length,
            'story_idea': response,
            'context_used': bool(context)
        }
    
    def develop_character(self, character_type: str, genre: str = "", background: str = "") -> Dict[str, Any]:
        """
        开发角色
        """
        context = self.get_knowledge_context(f"{character_type} {genre}", genre)
        
        context_section = f'📖 **参考资料分析**\n{context}\n' if context else ''
        genre_text = genre if genre else '小说'

        prompt = f"""作为角色塑造专家，请为我设计一个立体生动的{character_type}角色：

🎭 **角色设定需求**
- 角色类型：{character_type}
- 小说类型：{genre if genre else '通用类型'}
- 背景环境：{background if background else '现代都市背景'}

📋 **请按以下格式提供完整的角色档案**

## 👤 基本信息
- **姓名**：（含姓名寓意）
- **年龄**：
- **职业**：
- **社会地位**：
- **居住地**：

## 🎨 外貌特征
- **整体印象**：
- **显著特征**：（2-3个让人印象深刻的特点）
- **穿着风格**：
- **习惯动作**：

## 💭 性格特点
- **核心性格**：（3-5个主要特质）
- **性格优点**：
- **性格缺陷**：（让角色更真实的弱点）
- **说话方式**：（语言特色和口头禅）

## 📚 背景故事
- **成长环境**：
- **重要经历**：（塑造性格的关键事件）
- **教育背景**：
- **家庭关系**：

## 🎯 核心动机
- **表面目标**：（角色明确追求的）
- **深层需求**：（内心真正渴望的）
- **最大恐惧**：（最害怕失去的）
- **价值观念**：

## 📈 角色弧线
- **起始状态**：（故事开始时的状态）
- **转折点**：（促成改变的关键事件）
- **成长过程**：（如何克服困难）
- **最终状态**：（故事结束时的成长）

## 🤝 人际关系
- **与主角关系**：（如果不是主角）
- **重要关系网**：（家人、朋友、敌人）
- **社交特点**：（如何与他人相处）

## 💡 创作提示
- **写作重点**：
- **对话特色**：
- **行为模式**：
- **发展潜力**：

{context_section}

请确保角色具有独特性和可信度，适合在{genre_text}中发挥重要作用。"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'character_type': character_type,
            'genre': genre,
            'character_profile': response,
            'context_used': bool(context)
        }
    
    def improve_writing(self, text: str, improvement_type: str = "综合") -> Dict[str, Any]:
        """
        改进写作
        """
        prompt = f"""作为文本编辑专家，请帮我改进以下文本，重点关注{improvement_type}：

📝 **原文内容**
```
{text}
```

📋 **请按以下格式提供改进方案**

## 🔍 文本分析
- **当前优点**：（原文的可取之处）
- **主要问题**：（需要改进的地方）
- **改进重点**：（针对{improvement_type}的具体问题）

## ✏️ 修改版本
```
（这里提供改进后的完整文本）
```

## 📊 对比说明
| 改进方面 | 原文问题 | 修改方案 | 效果说明 |
|---------|---------|---------|---------|
| 示例项目 | 具体问题 | 具体修改 | 改进效果 |

## 💡 改进要点
1. **{improvement_type}方面**：
   - 具体改进点1
   - 具体改进点2
   - 具体改进点3

2. **整体提升**：
   - 语言流畅度
   - 表达准确性
   - 阅读体验

## 🎯 写作技巧提醒
- **针对{improvement_type}的技巧**：
- **通用写作建议**：
- **避免的常见错误**：

## 📚 延伸建议
- **进一步改进方向**：
- **相关写作资源**：
- **练习建议**：

请确保改进后的文本在保持原意的基础上，显著提升{improvement_type}效果。"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'original_text': text,
            'improvement_type': improvement_type,
            'improved_version': response
        }
    
    def plot_development(self, current_plot: str, direction: str = "") -> Dict[str, Any]:
        """
        情节发展建议
        """
        prompt = f"""作为情节发展顾问，请基于当前情节为故事提供专业的发展建议：

📖 **当前情节状况**
```
{current_plot}
```

🎯 **发展期望**：{direction if direction else '开放式发展，发挥创意'}

📋 **请按以下格式提供发展方案**

## 🔍 情节分析
- **当前状态**：（故事进展到什么程度）
- **已有冲突**：（现存的矛盾和张力）
- **角色状态**：（主要角色的当前处境）
- **待解决问题**：（需要推进的情节线）

## 🚀 发展方案（3-5个选项）

### 方案一：【方案名称】
- **发展路径**：（具体的情节走向）
- **关键事件**：（推动情节的重要事件）
- **角色变化**：（角色如何成长或改变）
- **优势分析**：（这个方向的优点）
- **风险提醒**：（可能的问题和挑战）

### 方案二：【方案名称】
（同上格式）

### 方案三：【方案名称】
（同上格式）

## ⭐ 推荐方案
- **最佳选择**：方案X
- **推荐理由**：
  1. 符合故事逻辑
  2. 增强戏剧张力
  3. 促进角色成长
  4. 满足读者期待

## 📝 写作要点
- **节奏控制**：（如何把握情节节奏）
- **冲突设计**：（如何设置和升级冲突）
- **角色发展**：（如何推动角色成长）
- **伏笔布局**：（需要埋下的伏笔）

## 🎭 场景建议
- **关键场景**：（重要的戏剧场景）
- **情感高潮**：（情感冲击点的设计）
- **转折点**：（故事转向的关键时刻）

## ⚠️ 注意事项
- **逻辑一致性**：（保持故事逻辑）
- **角色动机**：（确保角色行为合理）
- **读者体验**：（考虑读者的感受）
- **后续影响**：（对整体故事的影响）

请确保建议既有创意又符合故事的整体逻辑和风格。"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'current_plot': current_plot,
            'direction': direction,
            'suggestions': response
        }
    
    def writing_consultation(self, question: str, context: str = "") -> Dict[str, Any]:
        """
        写作咨询
        """
        # 尝试从知识库获取相关信息
        knowledge_context = self.get_knowledge_context(question)
        
        full_context = ""
        if context:
            full_context += f"用户背景：{context}\n\n"
        if knowledge_context:
            full_context += f"参考资料：{knowledge_context}\n\n"
        
        prompt = f"""{full_context}用户问题：{question}

请作为专业的小说写作专家，提供详细、实用的建议。"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        # 记录对话历史
        self.conversation_history.append({
            'question': question,
            'context': context,
            'response': response
        })
        
        return {
            'question': question,
            'answer': response,
            'knowledge_used': bool(knowledge_context)
        }
    
    def get_genre_guide(self, genre: str) -> Dict[str, Any]:
        """
        获取类型写作指南
        """
        # 获取该类型的示例作品
        examples = self.knowledge_manager.get_genre_examples(genre, 3)
        
        context = ""
        if examples:
            context = f"该类型的优秀作品示例：\n"
            for ex in examples:
                context += f"- 《{ex.get('title', '')}》：{ex.get('content', '')[:100]}...\n"
        
        prompt = f"""请提供{genre}小说的详细写作指南，包括：

1. 类型特点和核心要素
2. 常见的情节模式
3. 人物设定要点
4. 写作技巧和注意事项
5. 读者期待和市场特点
6. 成功案例分析

{context}"""
        
        response = self.llm_client.generate_response(prompt, self.system_prompt)
        
        return {
            'genre': genre,
            'guide': response,
            'examples_count': len(examples)
        }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        获取对话历史
        """
        return self.conversation_history
    
    def clear_history(self):
        """
        清空对话历史
        """
        self.conversation_history = []
