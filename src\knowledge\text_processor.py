"""
文本处理模块
负责文本预处理、分块和向量化
"""
import re
import jieba
from typing import List, Dict, Any
from config import Config

class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        self.chunk_size = Config.CHUNK_SIZE
        self.chunk_overlap = Config.CHUNK_OVERLAP
        
    def clean_text(self, text: str) -> str:
        """
        清理文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、数字和基本标点）
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,!?;:""''()（）【】《》\-]', '', text)
        
        # 移除过短的行
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 5]
        
        return '\n'.join(cleaned_lines)
    
    def split_text_into_chunks(self, text: str, title: str = "") -> List[Dict[str, Any]]:
        """
        将文本分割成块
        """
        if not text:
            return []
        
        # 清理文本
        cleaned_text = self.clean_text(text)
        
        chunks = []
        
        # 按段落分割
        paragraphs = [p.strip() for p in cleaned_text.split('\n') if p.strip()]
        
        current_chunk = ""
        current_length = 0
        
        for paragraph in paragraphs:
            paragraph_length = len(paragraph)
            
            # 如果当前块加上新段落超过限制，保存当前块并开始新块
            if current_length + paragraph_length > self.chunk_size and current_chunk:
                chunks.append({
                    'content': current_chunk.strip(),
                    'title': title,
                    'length': current_length
                })
                
                # 开始新块，保留重叠部分
                if self.chunk_overlap > 0:
                    overlap_text = current_chunk[-self.chunk_overlap:]
                    current_chunk = overlap_text + "\n" + paragraph
                    current_length = len(overlap_text) + paragraph_length
                else:
                    current_chunk = paragraph
                    current_length = paragraph_length
            else:
                # 添加到当前块
                if current_chunk:
                    current_chunk += "\n" + paragraph
                else:
                    current_chunk = paragraph
                current_length += paragraph_length
        
        # 添加最后一个块
        if current_chunk.strip():
            chunks.append({
                'content': current_chunk.strip(),
                'title': title,
                'length': current_length
            })
        
        return chunks
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """
        提取关键词
        """
        if not text:
            return []
        
        try:
            # 使用jieba进行分词
            words = jieba.lcut(text)
            
            # 过滤停用词和短词
            stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            
            filtered_words = [word for word in words if len(word) > 1 and word not in stop_words]
            
            # 统计词频
            word_freq = {}
            for word in filtered_words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # 按频率排序并返回前top_k个
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            
            return [word for word, freq in sorted_words[:top_k]]
            
        except Exception as e:
            print(f"提取关键词失败: {e}")
            return []
    
    def process_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理文档列表，返回处理后的文档块
        """
        processed_chunks = []
        
        for doc in documents:
            title = doc.get('title', '')
            content = doc.get('content', '')
            author = doc.get('author', '')
            genre = doc.get('genre', '')
            
            # 分割文本
            chunks = self.split_text_into_chunks(content, title)
            
            # 为每个块添加元数据
            for i, chunk in enumerate(chunks):
                chunk_data = {
                    'id': f"{title}_{i}",
                    'content': chunk['content'],
                    'title': title,
                    'author': author,
                    'genre': genre,
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'keywords': self.extract_keywords(chunk['content']),
                    'length': chunk['length']
                }
                processed_chunks.append(chunk_data)
        
        return processed_chunks
    
    def get_processing_stats(self, processed_chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取处理统计信息
        """
        if not processed_chunks:
            return {}
        
        total_chunks = len(processed_chunks)
        total_length = sum(chunk['length'] for chunk in processed_chunks)
        avg_length = total_length // total_chunks if total_chunks > 0 else 0
        
        # 统计类型分布
        genre_stats = {}
        for chunk in processed_chunks:
            genre = chunk.get('genre', '未知')
            genre_stats[genre] = genre_stats.get(genre, 0) + 1
        
        return {
            'total_chunks': total_chunks,
            'total_length': total_length,
            'average_length': avg_length,
            'genre_distribution': genre_stats
        }
