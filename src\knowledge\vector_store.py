"""
向量存储模块
简化版本，使用文本匹配进行检索
"""
import json
import os
from typing import List, Dict, Any, Tuple
from config import Config

class SimpleVectorStore:
    """
    简化的向量存储
    使用关键词匹配和文本相似度进行检索
    """
    
    def __init__(self):
        self.documents = []
        self.index_file = os.path.join(Config.CHROMA_DB_PATH, 'simple_index.json')
        self.ensure_db_dir()
    
    def ensure_db_dir(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.index_file)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """
        添加文档到存储
        """
        self.documents.extend(documents)
        print(f"添加了 {len(documents)} 个文档块到向量存储")
    
    def save_index(self):
        """
        保存索引到文件
        """
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, ensure_ascii=False, indent=2)
            print(f"索引已保存到: {self.index_file}")
        except Exception as e:
            print(f"保存索引失败: {e}")
    
    def load_index(self):
        """
        从文件加载索引
        """
        try:
            if os.path.exists(self.index_file):
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    self.documents = json.load(f)
                print(f"从 {self.index_file} 加载了 {len(self.documents)} 个文档")
            else:
                print("索引文件不存在，使用空索引")
        except Exception as e:
            print(f"加载索引失败: {e}")
            self.documents = []
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算文本相似度（简单的关键词匹配）
        """
        if not text1 or not text2:
            return 0.0
        
        # 转换为小写
        text1 = text1.lower()
        text2 = text2.lower()
        
        # 简单的词汇重叠计算
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def search(self, query: str, top_k: int = 5, genre_filter: str = None) -> List[Dict[str, Any]]:
        """
        搜索相关文档
        """
        if not self.documents:
            return []
        
        results = []
        
        for doc in self.documents:
            # 类型过滤
            if genre_filter and doc.get('genre', '').lower() != genre_filter.lower():
                continue
            
            # 计算相似度
            content_similarity = self.calculate_text_similarity(query, doc.get('content', ''))
            title_similarity = self.calculate_text_similarity(query, doc.get('title', ''))
            keywords_similarity = self.calculate_text_similarity(query, ' '.join(doc.get('keywords', [])))
            
            # 综合相似度（标题权重更高）
            total_similarity = content_similarity * 0.6 + title_similarity * 0.3 + keywords_similarity * 0.1
            
            if total_similarity > 0:
                result = doc.copy()
                result['similarity'] = total_similarity
                results.append(result)
        
        # 按相似度排序
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return results[:top_k]
    
    def search_by_genre(self, genre: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        按类型搜索文档
        """
        results = []
        for doc in self.documents:
            if doc.get('genre', '').lower() == genre.lower():
                results.append(doc)
                if len(results) >= limit:
                    break
        return results
    
    def search_by_author(self, author: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        按作者搜索文档
        """
        results = []
        for doc in self.documents:
            if author.lower() in doc.get('author', '').lower():
                results.append(doc)
                if len(results) >= limit:
                    break
        return results
    
    def get_random_samples(self, count: int = 3) -> List[Dict[str, Any]]:
        """
        获取随机样本
        """
        import random
        if len(self.documents) <= count:
            return self.documents
        return random.sample(self.documents, count)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        """
        if not self.documents:
            return {'total_documents': 0}
        
        total_docs = len(self.documents)
        genres = {}
        authors = {}
        
        for doc in self.documents:
            genre = doc.get('genre', '未知')
            genres[genre] = genres.get(genre, 0) + 1
            
            author = doc.get('author', '未知')
            authors[author] = authors.get(author, 0) + 1
        
        return {
            'total_documents': total_docs,
            'unique_genres': len(genres),
            'unique_authors': len(authors),
            'genre_distribution': genres,
            'author_distribution': authors
        }
    
    def clear(self):
        """
        清空存储
        """
        self.documents = []
        if os.path.exists(self.index_file):
            os.remove(self.index_file)
        print("向量存储已清空")
