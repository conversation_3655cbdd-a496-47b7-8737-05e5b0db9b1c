# 小说写作专家 - 使用指南

## 快速开始

### 1. 启动应用
```bash
# 方法1：使用主启动文件
python main.py

# 方法2：使用简化启动脚本（推荐）
python run_app.py
```

### 2. 访问应用
启动成功后，在浏览器中访问：`http://localhost:5000`

## 功能使用说明

### 🎯 故事创意生成
1. 点击左侧菜单的"故事创意生成"
2. 选择小说类型（如：现代都市、玄幻修仙等）
3. 选择作品长度（短篇、中篇、长篇）
4. 可选：输入主题或关键词
5. 点击"生成创意"按钮
6. 系统将生成包含故事梗概、人物设定、核心冲突等的完整创意

**示例输入：**
- 类型：现代都市
- 长度：中篇
- 主题：友情与成长

### 👤 角色开发
1. 点击"角色开发"功能
2. 选择角色类型（主角、配角、反派等）
3. 可选：选择小说类型
4. 可选：输入背景设定
5. 点击"开发角色"
6. 获得详细的角色档案，包括外貌、性格、背景故事等

**示例输入：**
- 角色类型：主角
- 小说类型：玄幻修仙
- 背景设定：出身平凡的少年，意外获得修仙机缘

### ✏️ 写作改进
1. 点击"写作改进"功能
2. 在文本框中粘贴需要改进的文本
3. 选择改进重点（综合、文笔、情节、对话、描写）
4. 点击"改进文本"
5. 获得具体的改进建议和修改版本

**适用场景：**
- 文笔不够流畅
- 对话显得生硬
- 描写缺乏画面感
- 情节推进有问题

### 🗺️ 情节发展（开发中）
帮助规划故事的后续发展方向

### ❓ 写作咨询（开发中）
解答各种写作相关问题

### 📚 类型指南（开发中）
提供不同小说类型的写作指导

## 系统状态说明

在左侧边栏的"系统状态"区域，您可以看到：
- **系统初始化**：应用是否正常启动
- **知识库**：知识库是否加载成功
- **AI模型**：DeepSeek API连接状态

状态指示：
- 🟢 绿色：正常运行
- 🔴 红色：离线或异常
- 🟡 黄色：警告状态

## 高级功能

### 知识库管理
系统会自动加载和处理知识库中的小说文本，用于：
- 提供写作参考
- 生成相关建议
- 增强创意质量

### API接口使用
如果您是开发者，可以直接调用API接口：

```bash
# 获取系统状态
curl http://localhost:5000/api/status

# 生成故事创意
curl -X POST http://localhost:5000/api/story-idea \
  -H "Content-Type: application/json" \
  -d '{"genre":"现代都市","theme":"友情","length":"中篇"}'
```

## 常见问题解决

### Q1: 应用启动失败
**解决方法：**
1. 检查Python环境和依赖安装
2. 运行 `python test_app.py` 进行诊断
3. 查看控制台错误信息

### Q2: AI生成内容质量不高
**可能原因：**
1. DeepSeek API连接问题（使用备用响应）
2. 输入信息不够详细
3. 知识库内容不足

**改进建议：**
1. 检查网络连接和API密钥
2. 提供更详细的输入信息
3. 添加更多相关的知识库内容

### Q3: 页面加载缓慢
**解决方法：**
1. 检查网络连接
2. 等待AI模型响应（通常需要几秒到几十秒）
3. 刷新页面重试

### Q4: 知识库无法加载
**检查项目：**
1. 知识库路径是否正确
2. 文件权限是否足够
3. 文件格式是否支持（.txt, .json, .md）

## 使用技巧

### 1. 故事创意生成技巧
- 尽量选择具体的类型，而不是"通用"
- 在主题中输入多个关键词，用逗号分隔
- 可以多次生成，选择最满意的创意

### 2. 角色开发技巧
- 在背景设定中提供详细的世界观信息
- 可以为同一个故事开发多个角色
- 注意角色之间的关系和冲突设置

### 3. 写作改进技巧
- 一次提交的文本不要太长（建议500字以内）
- 明确指定改进重点，获得更针对性的建议
- 可以多次改进，逐步完善

### 4. 提高生成质量
- 提供清晰、具体的输入信息
- 使用专业的写作术语
- 参考生成的内容，结合自己的创意

## 数据安全

- 您的输入内容会发送到DeepSeek API进行处理
- 本地不会永久存储您的创作内容
- 建议定期备份重要的创作成果

## 技术支持

如果遇到技术问题：
1. 查看控制台输出的错误信息
2. 运行测试脚本进行诊断
3. 检查README.md中的故障排除部分

---

**祝您创作愉快！** ✍️📚
