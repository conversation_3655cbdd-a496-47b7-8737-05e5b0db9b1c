"""
智能体服务层
提供统一的智能体接口
"""
from typing import Dict, Any, List
from .novel_writer_agent import NovelWriterAgent
from .sasha_agent import SashaAgent

class AgentService:
    """智能体服务"""
    
    def __init__(self):
        self.novel_writer = NovelWriterAgent()
        self.sasha = SashaAgent()
        self.service_status = {
            'initialized': False,
            'knowledge_base_ready': False,
            'llm_connection': False
        }
        self._initialize()
    
    def _initialize(self):
        """初始化服务"""
        try:
            # 检查知识库状态
            stats = self.novel_writer.knowledge_manager.get_statistics()
            self.service_status['knowledge_base_ready'] = stats.get('total_documents', 0) > 0
            
            # 检查LLM连接
            self.service_status['llm_connection'] = self.novel_writer.llm_client.test_connection()
            
            self.service_status['initialized'] = True
            print("智能体服务初始化完成")
            
        except Exception as e:
            print(f"智能体服务初始化失败: {e}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return self.service_status
    
    def generate_story_idea(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成故事创意
        
        Args:
            request: {
                'genre': str,           # 小说类型
                'theme': str,           # 主题（可选）
                'length': str,          # 长度（可选）
            }
        """
        try:
            genre = request.get('genre', '现代都市')
            theme = request.get('theme', '')
            length = request.get('length', '中篇')
            
            result = self.novel_writer.generate_story_idea(genre, theme, length)
            
            return {
                'success': True,
                'data': result,
                'message': '故事创意生成成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '故事创意生成失败'
            }
    
    def develop_character(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        开发角色
        
        Args:
            request: {
                'character_type': str,  # 角色类型
                'genre': str,           # 小说类型（可选）
                'background': str,      # 背景设定（可选）
            }
        """
        try:
            character_type = request.get('character_type', '主角')
            genre = request.get('genre', '')
            background = request.get('background', '')
            
            result = self.novel_writer.develop_character(character_type, genre, background)
            
            return {
                'success': True,
                'data': result,
                'message': '角色开发成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '角色开发失败'
            }
    
    def improve_writing(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        改进写作
        
        Args:
            request: {
                'text': str,                # 原文本
                'improvement_type': str,    # 改进类型（可选）
            }
        """
        try:
            text = request.get('text', '')
            if not text:
                return {
                    'success': False,
                    'error': '文本内容不能为空',
                    'message': '请提供需要改进的文本'
                }
            
            improvement_type = request.get('improvement_type', '综合')
            
            result = self.novel_writer.improve_writing(text, improvement_type)
            
            return {
                'success': True,
                'data': result,
                'message': '写作改进建议生成成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '写作改进失败'
            }
    
    def plot_development(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        情节发展建议
        
        Args:
            request: {
                'current_plot': str,    # 当前情节
                'direction': str,       # 发展方向（可选）
            }
        """
        try:
            current_plot = request.get('current_plot', '')
            if not current_plot:
                return {
                    'success': False,
                    'error': '当前情节不能为空',
                    'message': '请提供当前的情节内容'
                }
            
            direction = request.get('direction', '')
            
            result = self.novel_writer.plot_development(current_plot, direction)
            
            return {
                'success': True,
                'data': result,
                'message': '情节发展建议生成成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '情节发展建议生成失败'
            }
    
    def writing_consultation(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        写作咨询
        
        Args:
            request: {
                'question': str,    # 问题
                'context': str,     # 上下文（可选）
            }
        """
        try:
            question = request.get('question', '')
            if not question:
                return {
                    'success': False,
                    'error': '问题不能为空',
                    'message': '请提供您的问题'
                }
            
            context = request.get('context', '')
            
            result = self.novel_writer.writing_consultation(question, context)
            
            return {
                'success': True,
                'data': result,
                'message': '咨询回答生成成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '写作咨询失败'
            }
    
    def get_genre_guide(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取类型写作指南
        
        Args:
            request: {
                'genre': str,   # 小说类型
            }
        """
        try:
            genre = request.get('genre', '')
            if not genre:
                return {
                    'success': False,
                    'error': '小说类型不能为空',
                    'message': '请指定小说类型'
                }
            
            result = self.novel_writer.get_genre_guide(genre)
            
            return {
                'success': True,
                'data': result,
                'message': '类型指南获取成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '类型指南获取失败'
            }
    
    def get_available_genres(self) -> Dict[str, Any]:
        """获取可用的小说类型"""
        try:
            genres = self.novel_writer.knowledge_manager.get_available_genres()
            
            # 如果知识库中没有类型，提供默认类型
            if not genres:
                genres = ['现代都市', '古代言情', '玄幻修仙', '科幻未来', '悬疑推理', '青春校园', '历史军事']
            
            return {
                'success': True,
                'data': {'genres': genres},
                'message': '类型列表获取成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '类型列表获取失败'
            }
    
    def get_knowledge_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            stats = self.novel_writer.knowledge_manager.get_statistics()
            
            return {
                'success': True,
                'data': stats,
                'message': '知识库统计获取成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '知识库统计获取失败'
            }
    
    def get_conversation_history(self) -> Dict[str, Any]:
        """获取对话历史"""
        try:
            history = self.novel_writer.get_conversation_history()
            
            return {
                'success': True,
                'data': {'history': history},
                'message': '对话历史获取成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '对话历史获取失败'
            }
    
    def clear_conversation_history(self) -> Dict[str, Any]:
        """清空对话历史"""
        try:
            self.novel_writer.clear_history()

            return {
                'success': True,
                'data': {},
                'message': '对话历史已清空'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '清空对话历史失败'
            }

    def sasha_chat(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        与莎莎聊天

        Args:
            request: {
                'message': str,     # 用户消息
                'context': str,     # 上下文（可选）
            }
        """
        try:
            message = request.get('message', '')
            if not message:
                return {
                    'success': False,
                    'error': '消息不能为空',
                    'message': '请输入您想说的话'
                }

            context = request.get('context', '')

            result = self.sasha.chat(message, context)

            return {
                'success': True,
                'data': result,
                'message': '莎莎回复成功'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '莎莎聊天失败'
            }

    def get_sasha_history(self) -> Dict[str, Any]:
        """获取莎莎的对话历史"""
        try:
            history = self.sasha.get_conversation_history()
            stats = self.sasha.get_conversation_stats()

            return {
                'success': True,
                'data': {
                    'history': history,
                    'stats': stats
                },
                'message': '莎莎对话历史获取成功'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '获取莎莎对话历史失败'
            }

    def clear_sasha_history(self) -> Dict[str, Any]:
        """清空莎莎的对话历史"""
        try:
            self.sasha.clear_history()

            return {
                'success': True,
                'data': {},
                'message': '莎莎对话历史已清空'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '清空莎莎对话历史失败'
            }

    def get_sasha_info(self) -> Dict[str, Any]:
        """获取莎莎的个性信息"""
        try:
            personality = self.sasha.get_personality_info()

            return {
                'success': True,
                'data': personality,
                'message': '莎莎信息获取成功'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '获取莎莎信息失败'
            }
