{% extends "base.html" %}

{% block title %}小说写作专家 - 智能创作助手{% endblock %}

{% block content %}
<div class="row">
    <!-- 侧边栏 -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>功能菜单</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action" data-tab="story-idea">
                        <i class="fas fa-lightbulb me-2"></i>故事创意生成
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-tab="character">
                        <i class="fas fa-user me-2"></i>角色开发
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-tab="writing-improve">
                        <i class="fas fa-edit me-2"></i>写作改进
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-tab="plot-development">
                        <i class="fas fa-route me-2"></i>情节发展
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-tab="consultation">
                        <i class="fas fa-question-circle me-2"></i>写作咨询
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-tab="sasha-chat">
                        <i class="fas fa-heart me-2 text-danger"></i>莎莎助手
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 系统状态 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统状态</h6>
            </div>
            <div class="card-body">
                <div id="system-status">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                        <small class="d-block mt-2">检查中...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="col-md-9">
        <!-- 欢迎页面 -->
        <div id="welcome-tab" class="tab-content active">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-pen-fancy fa-4x text-primary mb-4"></i>
                    <h2 class="mb-3">欢迎使用小说写作专家</h2>
                    <p class="lead mb-4">基于LangChain和DeepSeek的智能写作助手，帮助您创作精彩的小说作品</p>
                    
                    <div class="row mt-5">
                        <div class="col-md-4">
                            <div class="feature-box">
                                <i class="fas fa-lightbulb fa-2x text-warning mb-3"></i>
                                <h5>创意生成</h5>
                                <p>根据您的需求生成独特的故事创意和情节构思</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-box">
                                <i class="fas fa-users fa-2x text-success mb-3"></i>
                                <h5>角色塑造</h5>
                                <p>帮助您创造立体生动的角色形象和人物关系</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-box">
                                <i class="fas fa-magic fa-2x text-info mb-3"></i>
                                <h5>写作指导</h5>
                                <p>提供专业的写作技巧和文笔改进建议</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button class="btn btn-primary btn-lg" onclick="showTab('story-idea')">
                            <i class="fas fa-rocket me-2"></i>开始创作
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 故事创意生成 -->
        <div id="story-idea-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>故事创意生成</h5>
                </div>
                <div class="card-body">
                    <form id="story-idea-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="genre" class="form-label">小说类型</label>
                                    <select class="form-select" id="genre" name="genre" required>
                                        <option value="">请选择类型</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="length" class="form-label">作品长度</label>
                                    <select class="form-select" id="length" name="length">
                                        <option value="短篇">短篇（1-5万字）</option>
                                        <option value="中篇" selected>中篇（5-15万字）</option>
                                        <option value="长篇">长篇（15万字以上）</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="theme" class="form-label">主题或关键词（可选）</label>
                            <input type="text" class="form-control" id="theme" name="theme" 
                                   placeholder="例如：友情、成长、冒险...">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-magic me-2"></i>生成创意
                        </button>
                    </form>
                    
                    <div id="story-idea-result" class="mt-4" style="display: none;">
                        <h6>生成的故事创意：</h6>
                        <div class="result-content"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 角色开发 -->
        <div id="character-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>角色开发</h5>
                </div>
                <div class="card-body">
                    <form id="character-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="character-type" class="form-label">角色类型</label>
                                    <select class="form-select" id="character-type" name="character_type" required>
                                        <option value="">请选择角色类型</option>
                                        <option value="主角">主角</option>
                                        <option value="配角">重要配角</option>
                                        <option value="反派">反派角色</option>
                                        <option value="导师">导师角色</option>
                                        <option value="朋友">朋友角色</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="character-genre" class="form-label">小说类型（可选）</label>
                                    <select class="form-select" id="character-genre" name="genre">
                                        <option value="">不限类型</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="character-background" class="form-label">背景设定（可选）</label>
                            <textarea class="form-control" id="character-background" name="background" rows="3"
                                      placeholder="描述角色的背景环境、时代设定等..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>开发角色
                        </button>
                    </form>
                    
                    <div id="character-result" class="mt-4" style="display: none;">
                        <h6>角色设定：</h6>
                        <div class="result-content"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 写作改进 -->
        <div id="writing-improve-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>写作改进</h5>
                </div>
                <div class="card-body">
                    <form id="writing-improve-form">
                        <div class="mb-3">
                            <label for="improve-text" class="form-label">需要改进的文本</label>
                            <textarea class="form-control" id="improve-text" name="text" rows="6" required
                                      placeholder="请粘贴您需要改进的文本内容..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="improvement-type" class="form-label">改进重点</label>
                            <select class="form-select" id="improvement-type" name="improvement_type">
                                <option value="综合">综合改进</option>
                                <option value="文笔">文笔优化</option>
                                <option value="情节">情节完善</option>
                                <option value="对话">对话改进</option>
                                <option value="描写">描写增强</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-magic me-2"></i>改进文本
                        </button>
                    </form>
                    
                    <div id="writing-improve-result" class="mt-4" style="display: none;">
                        <h6>改进建议：</h6>
                        <div class="result-content"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 情节发展建议 -->
        <div id="plot-development-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-route me-2"></i>情节发展建议</h5>
                </div>
                <div class="card-body">
                    <form id="plot-development-form">
                        <div class="mb-3">
                            <label for="current-plot" class="form-label">当前情节描述</label>
                            <textarea class="form-control" id="current-plot" name="current_plot" rows="6" required
                                      placeholder="请详细描述您当前的故事情节，包括主要角色、已发生的事件、当前的冲突等..."></textarea>
                            <div class="form-text">
                                <i class="fas fa-lightbulb text-warning"></i>
                                提示：描述得越详细，AI给出的建议就越精准
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="plot-direction" class="form-label">期望发展方向（可选）</label>
                                    <select class="form-select" id="plot-direction" name="direction">
                                        <option value="">让AI自由发挥</option>
                                        <option value="增加冲突">增加冲突和张力</option>
                                        <option value="角色成长">推动角色成长</option>
                                        <option value="揭示秘密">揭示隐藏的秘密</option>
                                        <option value="情感发展">发展角色关系</option>
                                        <option value="高潮准备">为高潮做准备</option>
                                        <option value="收束情节">开始收束情节</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="plot-style" class="form-label">故事风格</label>
                                    <select class="form-select" id="plot-style" name="style">
                                        <option value="">不限风格</option>
                                        <option value="悬疑紧张">悬疑紧张</option>
                                        <option value="温馨治愈">温馨治愈</option>
                                        <option value="激烈冲突">激烈冲突</option>
                                        <option value="细腻情感">细腻情感</option>
                                        <option value="幽默轻松">幽默轻松</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="plot-constraints" class="form-label">限制条件（可选）</label>
                            <textarea class="form-control" id="plot-constraints" name="constraints" rows="2"
                                      placeholder="例如：不能让某个角色死亡、需要在特定场景中发展、有字数限制等..."></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-magic me-2"></i>获取发展建议
                        </button>
                    </form>

                    <div id="plot-development-result" class="mt-4" style="display: none;">
                        <h6>情节发展建议：</h6>
                        <div class="result-content"></div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="copyToClipboard($('#plot-development-result .result-content').text())">
                                <i class="fas fa-copy me-1"></i>复制建议
                            </button>
                            <button class="btn btn-outline-success btn-sm ms-2" onclick="continueStoryDevelopment()">
                                <i class="fas fa-forward me-1"></i>继续发展
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 写作咨询 -->
        <div id="consultation-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>写作咨询</h5>
                </div>
                <div class="card-body">
                    <!-- 对话历史 -->
                    <div id="consultation-history" class="mb-4" style="max-height: 400px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; background-color: #f8f9fa;">
                        <div class="text-center text-muted">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <p>开始您的写作咨询吧！我会根据您的问题提供专业建议。</p>
                        </div>
                    </div>

                    <!-- 快速问题模板 -->
                    <div class="mb-3">
                        <label class="form-label">常见问题（点击快速提问）：</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-question"
                                    data-question="我是新手作者，应该从哪里开始学习写作？">
                                新手入门
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-question"
                                    data-question="如何让我的角色更加立体和真实？">
                                角色塑造
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-question"
                                    data-question="我的故事情节总是很平淡，如何增加冲突和张力？">
                                情节设计
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-question"
                                    data-question="如何写出自然流畅的对话？">
                                对话写作
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-question"
                                    data-question="如何克服写作瓶颈和灵感枯竭？">
                                写作瓶颈
                            </button>
                        </div>
                    </div>

                    <!-- 咨询表单 -->
                    <form id="consultation-form">
                        <div class="mb-3">
                            <label for="consultation-question" class="form-label">您的问题</label>
                            <textarea class="form-control" id="consultation-question" name="question" rows="3" required
                                      placeholder="请详细描述您的写作问题或困惑..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="consultation-context" class="form-label">背景信息（可选）</label>
                                    <textarea class="form-control" id="consultation-context" name="context" rows="2"
                                              placeholder="例如：您的写作经验、当前项目类型、遇到困难的具体情况等..."></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="consultation-type" class="form-label">问题类型</label>
                                    <select class="form-select" id="consultation-type" name="type">
                                        <option value="">通用咨询</option>
                                        <option value="技巧指导">写作技巧指导</option>
                                        <option value="情节问题">情节构思问题</option>
                                        <option value="角色问题">角色塑造问题</option>
                                        <option value="文笔问题">文笔表达问题</option>
                                        <option value="创作心理">创作心理问题</option>
                                        <option value="发表投稿">发表投稿相关</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>提问
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearConsultationHistory()">
                                <i class="fas fa-trash me-2"></i>清空对话
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        


        <!-- 莎莎助手 -->
        <div id="sasha-chat-tab" class="tab-content">
            <div class="card">
                <div class="card-header bg-gradient" style="background: linear-gradient(45deg, #ff6b9d, #ffc3e0);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-heart me-2"></i>莎莎助手
                        <small class="ms-2">你的贴心写作伙伴</small>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 莎莎介绍 -->
                    <div class="alert alert-info border-0" style="background: linear-gradient(45deg, #e3f2fd, #f3e5f5);">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="avatar-circle">
                                    <i class="fas fa-user-circle fa-3x text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1">嗨！我是莎莎 💕</h6>
                                <p class="mb-0 small">
                                    我是你的专属写作助手，不仅可以帮你解决写作技巧问题，
                                    还可以陪你聊天解闷哦～有什么想说的都可以告诉我！
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 对话区域 -->
                    <div id="sasha-chat-history" class="mb-4" style="max-height: 400px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; background-color: #fafafa;">
                        <div class="chat-message sasha-message">
                            <div class="message-avatar">
                                <i class="fas fa-heart text-danger"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-bubble sasha-bubble">
                                    <p class="mb-1">你好呀！我是莎莎，很高兴认识你～</p>
                                    <p class="mb-0">你可以问我任何关于写作的问题，或者就是想聊聊天也可以哦！比如：</p>
                                    <ul class="mb-0 mt-2">
                                        <li>写作技巧和方法</li>
                                        <li>创作灵感和想法</li>
                                        <li>日常生活和心情</li>
                                        <li>读书心得和感悟</li>
                                    </ul>
                                </div>
                                <small class="text-muted">刚刚</small>
                            </div>
                        </div>
                    </div>

                    <!-- 快速话题 -->
                    <div class="mb-3">
                        <label class="form-label">快速话题：</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-pink btn-sm quick-topic"
                                    data-message="莎莎，我刚开始写小说，有点紧张，你能给我一些鼓励吗？">
                                <i class="fas fa-heart me-1"></i>新手鼓励
                            </button>
                            <button type="button" class="btn btn-outline-pink btn-sm quick-topic"
                                    data-message="莎莎，我今天没有写作灵感，感觉很沮丧...">
                                <i class="fas fa-cloud-rain me-1"></i>没有灵感
                            </button>
                            <button type="button" class="btn btn-outline-pink btn-sm quick-topic"
                                    data-message="莎莎，你觉得什么样的小说最吸引人？">
                                <i class="fas fa-star me-1"></i>写作心得
                            </button>
                            <button type="button" class="btn btn-outline-pink btn-sm quick-topic"
                                    data-message="莎莎，我们聊聊天吧，你最近在做什么？">
                                <i class="fas fa-comments me-1"></i>日常聊天
                            </button>
                        </div>
                    </div>

                    <!-- 聊天输入 -->
                    <form id="sasha-chat-form">
                        <div class="input-group">
                            <textarea class="form-control" id="sasha-message" name="message" rows="2"
                                      placeholder="和莎莎说点什么吧..." required></textarea>
                            <button type="submit" class="btn btn-pink">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle text-info"></i>
                            莎莎会记住我们的对话内容，让聊天更自然哦～
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
