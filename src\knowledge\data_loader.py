"""
知识库数据加载器
从ModelScope加载小说数据集
"""
import os
import json
import pandas as pd
from typing import List, Dict, Any
from config import Config

class NovelDataLoader:
    """小说数据加载器"""
    
    def __init__(self):
        self.knowledge_base_path = Config.KNOWLEDGE_BASE_PATH
        self.data = []
        
    def load_from_modelscope(self) -> List[Dict[str, Any]]:
        """
        从ModelScope加载小说数据集
        注意：由于网络问题，这里提供备用的本地加载方案
        """
        try:
            # 尝试从ModelScope加载
            from modelscope.msdatasets import MsDataset
            ds = MsDataset.load('gguava/xiaoshuo', subset_name='default', split='train')
            
            # 转换为列表格式
            data = []
            for item in ds:
                data.append({
                    'title': item.get('title', ''),
                    'content': item.get('content', ''),
                    'author': item.get('author', ''),
                    'genre': item.get('genre', ''),
                    'summary': item.get('summary', '')
                })
            
            self.data = data
            print(f"成功从ModelScope加载 {len(data)} 条小说数据")
            return data
            
        except Exception as e:
            print(f"从ModelScope加载数据失败: {e}")
            return self.load_from_local()
    
    def load_from_local(self) -> List[Dict[str, Any]]:
        """
        从本地知识库路径加载数据
        """
        if not os.path.exists(self.knowledge_base_path):
            print(f"本地知识库路径不存在: {self.knowledge_base_path}")
            return self.create_sample_data()
        
        data = []
        try:
            # 遍历知识库目录，查找文本文件
            for root, dirs, files in os.walk(self.knowledge_base_path):
                for file in files:
                    if file.endswith(('.txt', '.json', '.md')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                data.append({
                                    'title': os.path.splitext(file)[0],
                                    'content': content,
                                    'author': '未知',
                                    'genre': '小说',
                                    'summary': content[:200] + '...' if len(content) > 200 else content,
                                    'source': file_path
                                })
                        except Exception as e:
                            print(f"读取文件 {file_path} 失败: {e}")
            
            self.data = data
            print(f"成功从本地加载 {len(data)} 条小说数据")
            return data
            
        except Exception as e:
            print(f"从本地加载数据失败: {e}")
            return self.create_sample_data()
    
    def create_sample_data(self) -> List[Dict[str, Any]]:
        """
        创建示例数据用于测试
        """
        sample_data = [
            {
                'title': '示例小说1：青春校园',
                'content': '''这是一个关于青春校园的故事。主人公李明是一个普通的高中生，他在学校里遇到了许多有趣的同学和老师。
                
在一个阳光明媚的早晨，李明走进了教室，发现桌子上放着一封神秘的信件。信件的内容让他感到既兴奋又紧张...

故事围绕着友情、学习和成长展开，展现了青春期学生的内心世界和成长历程。''',
                'author': '示例作者',
                'genre': '青春校园',
                'summary': '一个关于高中生李明的青春成长故事，充满友情与梦想。'
            },
            {
                'title': '示例小说2：都市言情',
                'content': '''在繁华的都市中，年轻的设计师张雨遇到了神秘的企业家王浩。两人的相遇改变了彼此的人生轨迹。
                
张雨是一个独立自强的女性，她用自己的才华在设计界闯出了一片天地。而王浩则是商界的传奇人物，年轻有为却内心孤独...

这是一个关于爱情、事业和自我实现的现代都市故事。''',
                'author': '示例作者',
                'genre': '都市言情',
                'summary': '都市设计师张雨与企业家王浩的爱情故事，展现现代都市人的情感世界。'
            },
            {
                'title': '示例小说3：玄幻修仙',
                'content': '''在遥远的修仙世界中，少年林风意外获得了古老的修炼秘籍。从此，他踏上了修仙之路。
                
修仙路上充满了危险和机遇，林风不仅要面对强大的敌人，还要克服内心的恐惧和迷茫。在师父的指导下，他逐渐成长为一名强大的修仙者...

这是一个充满想象力的玄幻世界，展现了主人公的成长历程和修仙之路的艰辛。''',
                'author': '示例作者',
                'genre': '玄幻修仙',
                'summary': '少年林风的修仙之路，充满冒险与成长的玄幻故事。'
            }
        ]
        
        self.data = sample_data
        print(f"创建了 {len(sample_data)} 条示例数据")
        return sample_data
    
    def get_data(self) -> List[Dict[str, Any]]:
        """获取加载的数据"""
        if not self.data:
            return self.load_from_modelscope()
        return self.data
    
    def save_to_json(self, filepath: str):
        """将数据保存为JSON文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到: {filepath}")
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        if not self.data:
            return {}
        
        total_count = len(self.data)
        genres = {}
        authors = {}
        total_content_length = 0
        
        for item in self.data:
            # 统计类型
            genre = item.get('genre', '未知')
            genres[genre] = genres.get(genre, 0) + 1
            
            # 统计作者
            author = item.get('author', '未知')
            authors[author] = authors.get(author, 0) + 1
            
            # 统计内容长度
            content_length = len(item.get('content', ''))
            total_content_length += content_length
        
        return {
            'total_count': total_count,
            'genres': genres,
            'authors': authors,
            'average_content_length': total_content_length // total_count if total_count > 0 else 0,
            'total_content_length': total_content_length
        }
