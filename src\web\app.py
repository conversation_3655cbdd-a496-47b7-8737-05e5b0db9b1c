"""
Flask Web应用
"""
from flask import Flask, render_template, request, jsonify, session
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config import Config
from src.agents.agent_service import AgentService

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化智能体服务
    agent_service = AgentService()
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')
    
    @app.route('/api/status')
    def api_status():
        """获取服务状态"""
        try:
            status = agent_service.get_service_status()
            return jsonify({
                'success': True,
                'data': status,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/genres')
    def api_genres():
        """获取可用的小说类型"""
        try:
            result = agent_service.get_available_genres()
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/story-idea', methods=['POST'])
    def api_story_idea():
        """生成故事创意"""
        try:
            data = request.get_json()
            result = agent_service.generate_story_idea(data)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/character', methods=['POST'])
    def api_character():
        """开发角色"""
        try:
            data = request.get_json()
            result = agent_service.develop_character(data)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/improve-writing', methods=['POST'])
    def api_improve_writing():
        """改进写作"""
        try:
            data = request.get_json()
            result = agent_service.improve_writing(data)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/plot-development', methods=['POST'])
    def api_plot_development():
        """情节发展建议"""
        try:
            data = request.get_json()
            result = agent_service.plot_development(data)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/consultation', methods=['POST'])
    def api_consultation():
        """写作咨询"""
        try:
            data = request.get_json()
            result = agent_service.writing_consultation(data)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/genre-guide', methods=['POST'])
    def api_genre_guide():
        """获取类型写作指南"""
        try:
            data = request.get_json()
            result = agent_service.get_genre_guide(data)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/knowledge-stats')
    def api_knowledge_stats():
        """获取知识库统计"""
        try:
            result = agent_service.get_knowledge_stats()
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/history')
    def api_history():
        """获取对话历史"""
        try:
            result = agent_service.get_conversation_history()
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/clear-history', methods=['POST'])
    def api_clear_history():
        """清空对话历史"""
        try:
            result = agent_service.clear_conversation_history()
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.errorhandler(404)
    def not_found(error):
        return render_template('error.html', error_code=404, error_message='页面未找到'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('error.html', error_code=500, error_message='服务器内部错误'), 500
    
    return app
