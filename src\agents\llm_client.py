"""
LLM客户端
与DeepSeek API交互
"""
import requests
import json
from typing import Dict, Any, List
from config import Config

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        self.api_key = Config.DEEPSEEK_API_KEY
        self.base_url = Config.DEEPSEEK_BASE_URL
        self.max_tokens = Config.MAX_TOKENS
        self.temperature = Config.TEMPERATURE
        
    def _make_request(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        发送请求到DeepSeek API
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'deepseek-chat',
            'messages': messages,
            'max_tokens': kwargs.get('max_tokens', self.max_tokens),
            'temperature': kwargs.get('temperature', self.temperature),
            'stream': False
        }
        
        try:
            response = requests.post(
                f'{self.base_url}/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
                return self._get_fallback_response(messages)
                
        except Exception as e:
            print(f"API请求异常: {e}")
            return self._get_fallback_response(messages)
    
    def _get_fallback_response(self, messages: List[Dict[str, str]]) -> str:
        """
        当API不可用时的备用响应
        """
        user_message = ""
        for msg in messages:
            if msg['role'] == 'user':
                user_message = msg['content']
                break
        
        # 简单的关键词匹配生成响应
        if '小说' in user_message or '故事' in user_message:
            if '开头' in user_message or '开始' in user_message:
                return """这里是一个小说开头的示例：

在那个雨夜，李明站在窗前，看着街道上匆忙的行人。他不知道，这个普通的夜晚将会改变他的一生。

突然，一个神秘的包裹出现在他的门前，里面装着一封来自未知寄件人的信...

这样的开头设置了悬念，引起读者的好奇心，是小说创作的经典手法。"""
            
            elif '人物' in user_message or '角色' in user_message:
                return """人物塑造是小说创作的核心。以下是一些建议：

1. **主角设定**：给主角一个明确的目标和动机
2. **性格特点**：让角色有独特的性格特征和说话方式
3. **背景故事**：为角色设计丰富的过往经历
4. **成长弧线**：让角色在故事中有所成长和变化

例如：一个内向的程序员，因为一次意外事件，被迫踏上冒险之旅，最终成长为勇敢的领导者。"""
            
            elif '情节' in user_message or '剧情' in user_message:
                return """情节构建的基本要素：

1. **起因**：故事的触发事件
2. **发展**：矛盾冲突的展开
3. **高潮**：故事的转折点
4. **结局**：问题的解决

建议使用三幕式结构：
- 第一幕：设定背景，介绍角色
- 第二幕：发展冲突，推进剧情
- 第三幕：解决冲突，收束故事"""
            
            else:
                return """作为小说写作专家，我可以帮助您：

1. 构思故事情节和人物设定
2. 提供不同类型小说的写作技巧
3. 分析优秀作品的写作手法
4. 解决创作过程中的问题

请告诉我您想要创作什么类型的小说，或者您在创作过程中遇到了什么具体问题？"""
        
        else:
            return "我是小说写作专家，专门帮助您进行小说创作。请告诉我您的具体需求，比如想要创作什么类型的小说，或者在创作过程中遇到了什么问题？"
    
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> str:
        """
        生成响应
        """
        messages = []
        
        if context:
            messages.append({
                'role': 'system',
                'content': context
            })
        
        messages.append({
            'role': 'user',
            'content': prompt
        })
        
        return self._make_request(messages, **kwargs)
    
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        多轮对话
        """
        return self._make_request(messages, **kwargs)
    
    def test_connection(self) -> bool:
        """
        测试API连接
        """
        try:
            response = self.generate_response("你好", max_tokens=10)
            return len(response) > 0
        except:
            return False
