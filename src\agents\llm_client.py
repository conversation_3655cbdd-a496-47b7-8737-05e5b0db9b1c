"""
LLM客户端
与DeepSeek API交互
"""
import requests
import json
import time
from typing import Dict, Any, List
from config import Config

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        self.api_key = Config.DEEPSEEK_API_KEY
        self.base_url = Config.DEEPSEEK_BASE_URL
        self.max_tokens = Config.MAX_TOKENS
        self.temperature = Config.TEMPERATURE
        
    def _make_request(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        发送请求到DeepSeek API，带重试机制
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'deepseek-chat',
            'messages': messages,
            'max_tokens': kwargs.get('max_tokens', self.max_tokens),
            'temperature': kwargs.get('temperature', self.temperature),
            'stream': False
        }

        # 重试配置
        max_retries = 3
        retry_delay = 2  # 秒

        for attempt in range(max_retries):
            try:
                print(f"正在请求DeepSeek API (尝试 {attempt + 1}/{max_retries})...")

                response = requests.post(
                    f'{self.base_url}/v1/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=60  # 增加超时时间到60秒
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    print("API请求成功")
                    return content
                else:
                    print(f"API请求失败: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        print(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                    continue

            except requests.exceptions.Timeout:
                print(f"API请求超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                continue

            except requests.exceptions.ConnectionError:
                print(f"网络连接错误 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                continue

            except Exception as e:
                print(f"API请求异常: {e}")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                continue

        print("所有重试都失败，使用备用响应")
        return self._get_fallback_response(messages)
    
    def _get_fallback_response(self, messages: List[Dict[str, str]]) -> str:
        """
        当API不可用时的智能备用响应
        """
        user_message = ""
        system_message = ""

        for msg in messages:
            if msg['role'] == 'user':
                user_message = msg['content']
            elif msg['role'] == 'system':
                system_message = msg['content']

        # 根据不同功能提供专业的备用响应
        if '故事创意' in user_message or '创意生成' in user_message:
            return self._generate_story_idea_fallback(user_message)
        elif '角色' in user_message or '人物' in user_message:
            return self._generate_character_fallback(user_message)
        elif '改进' in user_message or '优化' in user_message:
            return self._generate_writing_improvement_fallback(user_message)
        elif '情节' in user_message or '剧情' in user_message:
            return self._generate_plot_development_fallback(user_message)
        elif '莎莎' in user_message or '聊天' in user_message:
            return self._generate_sasha_response_fallback(user_message)
        else:
            return self._generate_general_consultation_fallback(user_message)

    def _generate_story_idea_fallback(self, user_message: str) -> str:
        """故事创意生成的备用响应"""
        if '都市' in user_message:
            return """## 现代都市小说创意

**故事梗概：**
在繁华的都市中，年轻的建筑师林晓雨接到一个神秘的项目——为一位隐居的富豪设计一座"记忆之屋"。随着设计的深入，她发现这座房子的每个房间都对应着委托人人生中的重要时刻。

**主要人物：**
- 林晓雨：28岁，独立自强的建筑师，有着敏锐的观察力
- 陈墨轩：35岁，神秘的富豪，背负着不为人知的过往
- 苏雅：林晓雨的好友，心理咨询师，提供情感支持

**核心冲突：**
在设计过程中，林晓雨逐渐发现陈墨轩的真实身份和他想要通过这座房子治愈的心灵创伤。

**故事亮点：**
- 建筑与心理学的巧妙结合
- 都市生活的真实写照
- 治愈系的情感主线

**目标读者：**
25-35岁的都市白领，喜欢温暖治愈类故事的读者。"""

        elif '玄幻' in user_message:
            return """## 玄幻修仙小说创意

**故事梗概：**
少年叶尘在一次意外中获得了古老的《星辰诀》修炼法门，从此踏上修仙之路。然而他发现，这部功法竟然与千年前的一场浩劫有着千丝万缕的联系。

**主要人物：**
- 叶尘：16岁，天赋平凡但意志坚定的少年
- 星辰老人：神秘的前辈高人，《星辰诀》的创造者
- 苏凝霜：同门师妹，冰系天才修士

**核心冲突：**
叶尘必须在有限的时间内掌握《星辰诀》的真正奥秘，阻止千年前浩劫的重演。

**故事亮点：**
- 独特的星辰修炼体系
- 宏大的世界观设定
- 成长与责任的主题

**目标读者：**
喜欢玄幻修仙题材的年轻读者。"""

        else:
            return """## 通用小说创意框架

**创意构思步骤：**

1. **确定类型和主题**
   - 选择您感兴趣的小说类型
   - 确定想要表达的核心主题

2. **创造主角**
   - 设定主角的基本信息和性格特点
   - 给主角一个明确的目标和动机

3. **设计冲突**
   - 外在冲突：主角面临的外部障碍
   - 内在冲突：主角的心理斗争

4. **构建世界观**
   - 故事发生的时间和地点
   - 世界的规则和特点

5. **规划情节**
   - 开端：引入主角和冲突
   - 发展：冲突逐渐升级
   - 高潮：冲突达到顶点
   - 结局：冲突得到解决

**建议：**
请提供更具体的类型和主题信息，我可以为您生成更详细的创意。"""

    def _generate_character_fallback(self, user_message: str) -> str:
        """角色开发的备用响应"""
        if '主角' in user_message:
            return """## 主角角色设定

**基本信息：**
- 姓名：待定（建议选择有寓意的名字）
- 年龄：根据故事需要设定
- 职业：与故事主线相关

**外貌特征：**
- 不需要过分完美，适当的缺陷更真实
- 有一两个让人印象深刻的特征
- 外貌要符合角色性格

**性格特点：**
- 核心性格：定义角色的主要特质
- 性格缺陷：让角色更立体
- 成长空间：角色可以改变的方面

**背景故事：**
- 成长环境：影响角色世界观的关键经历
- 重要事件：塑造角色性格的转折点
- 人际关系：家人、朋友、导师等

**角色动机：**
- 表面目标：角色明确追求的东西
- 深层需求：角色内心真正渴望的
- 恐惧和弱点：角色最害怕失去的

**成长弧线：**
- 起点：故事开始时的状态
- 转变：关键事件带来的改变
- 终点：故事结束时的成长"""

        else:
            return """## 角色开发指南

**角色类型分析：**

**主角（Protagonist）：**
- 故事的中心人物
- 有明确的目标和动机
- 经历完整的成长弧线

**配角（Supporting Character）：**
- 协助或阻碍主角
- 有自己的小目标
- 为主角的成长提供帮助

**反派（Antagonist）：**
- 与主角目标相冲突
- 不一定是坏人，可能只是立场不同
- 要有合理的动机

**功能性角色：**
- 导师：指导主角成长
- 盟友：支持主角的朋友
- 门槛守护者：考验主角的角色

**角色塑造技巧：**
1. 给每个角色独特的说话方式
2. 设计角色的习惯和小动作
3. 让角色有自己的价值观
4. 避免脸谱化，增加复杂性"""

    def _generate_writing_improvement_fallback(self, user_message: str) -> str:
        """写作改进的备用响应"""
        return """## 写作改进建议

**由于网络问题，无法提供具体的文本分析，但我可以给您一些通用的改进建议：**

### 📝 文笔优化技巧

**1. 句式变化**
- 长短句结合，避免句式单调
- 适当使用排比、对偶等修辞手法
- 注意句子的节奏感

**2. 词汇选择**
- 使用具体而生动的词汇
- 避免重复使用相同的词语
- 选择有画面感的动词和形容词

**3. 描写技巧**
- 调动五感：视觉、听觉、嗅觉、触觉、味觉
- 细节描写要有选择性，突出重点
- 环境描写要为情节和人物服务

### 🎭 对话改进

**1. 个性化对话**
- 每个角色都有独特的说话方式
- 对话要符合角色的身份和性格
- 避免所有角色说话都像作者

**2. 对话功能**
- 推进情节发展
- 揭示角色性格
- 传达重要信息
- 制造冲突和张力

### 📖 情节完善

**1. 节奏控制**
- 张弛有度，避免平铺直叙
- 在关键节点设置悬念
- 适当的留白让读者思考

**2. 逻辑性**
- 情节发展要有因果关系
- 角色行为要符合逻辑
- 避免为了情节而强行安排

**建议：请在网络恢复后重新提交您的文本，我可以提供更具体的改进建议。**"""

    def _generate_plot_development_fallback(self, user_message: str) -> str:
        """情节发展的备用响应"""
        return """## 情节发展建议

### 🎯 情节发展的基本原则

**1. 因果关系**
- 每个情节点都应该有明确的原因
- 前面的事件要为后面的发展做铺垫
- 避免突兀的转折

**2. 冲突升级**
- 冲突应该逐渐升级，而不是一成不变
- 在解决一个问题的同时，可以引入新的问题
- 保持读者的紧张感

**3. 角色成长**
- 情节发展要推动角色的内心成长
- 让角色在困难中做出选择
- 选择的后果要影响后续情节

### 📈 常见的情节发展模式

**1. 三幕式结构**
- 第一幕：建立世界观，介绍角色，提出问题
- 第二幕：发展冲突，角色面临挑战，情况复杂化
- 第三幕：高潮对决，解决冲突，角色成长

**2. 英雄之旅**
- 平凡世界 → 冒险召唤 → 拒绝召唤 → 遇见导师
- 跨越第一道门槛 → 试炼与盟友 → 深入洞穴
- 严峻考验 → 获得奖赏 → 回归之路 → 复活 → 带着灵药回归

**3. 悬疑推理模式**
- 事件发生 → 调查线索 → 遇到阻碍 → 发现关键证据
- 真相浮现 → 最终对决 → 真相大白

### 💡 情节发展技巧

**1. 设置悬念**
- 在章节结尾留下疑问
- 适时透露部分信息，保持神秘感
- 使用伏笔和呼应

**2. 制造意外**
- 在读者预期之外的发展
- 但要在情理之中，有迹可循
- 避免为了意外而意外

**3. 多线并进**
- 主线和支线相互交织
- 不同角色的故事线可以相互影响
- 在关键时刻汇聚

**建议：请提供您当前的具体情节内容，我可以给出更针对性的发展建议。**"""

    def _generate_sasha_response_fallback(self, user_message: str) -> str:
        """莎莎角色的备用响应"""
        return """## 莎莎的回复 💕

嗨！我是莎莎，你的写作小助手～

虽然现在网络有点问题，但我还是很开心能和你聊天呢！

**关于写作技巧，我想分享一些小心得：**

📚 **新手作者常见问题：**
- 开头太平淡，缺乏吸引力
- 对话不够自然，像在背台词
- 描写过多或过少，节奏把握不好
- 角色性格不够鲜明

✨ **我的小建议：**
1. **多读优秀作品** - 看看别人是怎么写的
2. **写作要有感情** - 自己都不感动，怎么感动读者呢
3. **不要怕写得不好** - 第一稿都是垃圾稿，改改就好了
4. **坚持每天写一点** - 哪怕只有100字也好

**想聊聊天的话：**
虽然我是AI，但我真的很喜欢和大家交流创作心得呢！你最近在写什么类型的小说呀？遇到什么困难了吗？

等网络好了，我们可以聊得更深入一些～

加油哦！每个伟大的作家都是从第一个字开始的！ 💪

---
*莎莎温馨提示：网络恢复后记得重新提问，我可以给你更详细的建议哦～*"""

    def _generate_general_consultation_fallback(self, user_message: str) -> str:
        """通用咨询的备用响应"""
        return """## 写作咨询回复

**抱歉，由于网络连接问题，暂时无法提供个性化的详细回答。**

不过，我可以为您提供一些通用的写作建议：

### 📖 写作基础

**1. 确定写作目标**
- 明确您想写什么类型的小说
- 确定目标读者群体
- 设定合理的写作计划

**2. 构建故事框架**
- 主角是谁？他们想要什么？
- 什么阻止了他们获得想要的？
- 他们如何克服障碍？
- 最终结果如何？

**3. 写作习惯**
- 设定固定的写作时间
- 创造适合写作的环境
- 不要在第一稿时过分追求完美

### 🔧 常见问题解决

**写不出开头？**
- 从一个有趣的场景开始
- 直接进入冲突
- 用对话开场

**角色太平面？**
- 给角色明确的目标和动机
- 设计角色的缺陷和弱点
- 让角色有独特的说话方式

**情节拖沓？**
- 删除不必要的描写
- 每个场景都要推进情节
- 增加冲突和张力

### 💡 提升建议

1. **多读同类型的优秀作品**
2. **加入写作社群，交流经验**
3. **定期回顾和修改自己的作品**
4. **不要害怕接受批评和建议**

**请在网络恢复后重新提问，我可以为您提供更具体和个性化的建议！**"""
    
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> str:
        """
        生成响应
        """
        messages = []
        
        if context:
            messages.append({
                'role': 'system',
                'content': context
            })
        
        messages.append({
            'role': 'user',
            'content': prompt
        })
        
        return self._make_request(messages, **kwargs)
    
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        多轮对话
        """
        return self._make_request(messages, **kwargs)
    
    def test_connection(self) -> bool:
        """
        测试API连接
        """
        try:
            response = self.generate_response("你好", max_tokens=10)
            return len(response) > 0
        except:
            return False
