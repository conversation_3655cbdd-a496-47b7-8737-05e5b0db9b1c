// 主要JavaScript功能

$(document).ready(function() {
    // 初始化应用
    initApp();
    
    // 绑定事件
    bindEvents();
    
    // 加载系统状态
    loadSystemStatus();
    
    // 加载小说类型
    loadGenres();
});

// 初始化应用
function initApp() {
    console.log('小说写作专家应用初始化...');
    
    // 显示欢迎页面
    showTab('welcome');
}

// 绑定事件
function bindEvents() {
    // 侧边栏菜单点击
    $('.list-group-item[data-tab]').click(function(e) {
        e.preventDefault();
        const tabName = $(this).data('tab');
        showTab(tabName);
        
        // 更新菜单状态
        $('.list-group-item').removeClass('active');
        $(this).addClass('active');
    });
    
    // 表单提交事件
    $('#story-idea-form').submit(function(e) {
        e.preventDefault();
        generateStoryIdea();
    });
    
    $('#character-form').submit(function(e) {
        e.preventDefault();
        developCharacter();
    });
    
    $('#writing-improve-form').submit(function(e) {
        e.preventDefault();
        improveWriting();
    });

    $('#plot-development-form').submit(function(e) {
        e.preventDefault();
        plotDevelopment();
    });

    $('#consultation-form').submit(function(e) {
        e.preventDefault();
        submitConsultation();
    });

    $('#genre-guide-form').submit(function(e) {
        e.preventDefault();
        getGenreGuide();
    });

    $('#sasha-chat-form').submit(function(e) {
        e.preventDefault();
        sendSashaMessage();
    });

    // 快速问题点击
    $('.quick-question').click(function() {
        const question = $(this).data('question');
        $('#consultation-question').val(question);
        submitConsultation();
    });

    // 快速话题点击
    $('.quick-topic').click(function() {
        const message = $(this).data('message');
        $('#sasha-message').val(message);
        sendSashaMessage();
    });
}

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签页
    $('.tab-content').removeClass('active');
    
    // 显示指定标签页
    $(`#${tabName}-tab`).addClass('active');
    
    // 更新菜单状态
    $('.list-group-item').removeClass('active');
    $(`.list-group-item[data-tab="${tabName}"]`).addClass('active');
}

// 加载系统状态
function loadSystemStatus() {
    $.get('/api/status')
        .done(function(response) {
            if (response.success) {
                displaySystemStatus(response.data);
            } else {
                showError('获取系统状态失败');
            }
        })
        .fail(function() {
            showError('无法连接到服务器');
        });
}

// 显示系统状态
function displaySystemStatus(status) {
    let html = '';
    
    // 初始化状态
    html += createStatusItem('系统初始化', status.initialized);
    html += createStatusItem('知识库', status.knowledge_base_ready);
    html += createStatusItem('AI模型', status.llm_connection);
    
    $('#system-status').html(html);
}

// 创建状态项
function createStatusItem(label, isOnline) {
    const statusClass = isOnline ? 'online' : 'offline';
    const statusText = isOnline ? '正常' : '离线';
    
    return `
        <div class="status-item">
            <span>${label}</span>
            <span>
                <span class="status-indicator ${statusClass}"></span>
                <small class="ms-1">${statusText}</small>
            </span>
        </div>
    `;
}

// 加载小说类型
function loadGenres() {
    $.get('/api/genres')
        .done(function(response) {
            if (response.success) {
                const genres = response.data.genres;
                populateGenreSelects(genres);
            }
        })
        .fail(function() {
            console.log('加载类型列表失败，使用默认类型');
            const defaultGenres = ['现代都市', '古代言情', '玄幻修仙', '科幻未来', '悬疑推理', '青春校园'];
            populateGenreSelects(defaultGenres);
        });
}

// 填充类型选择框
function populateGenreSelects(genres) {
    const selects = ['#genre', '#character-genre'];
    
    selects.forEach(selector => {
        const $select = $(selector);
        const currentValue = $select.val();
        
        // 清空现有选项（保留第一个默认选项）
        $select.find('option:not(:first)').remove();
        
        // 添加类型选项
        genres.forEach(genre => {
            $select.append(`<option value="${genre}">${genre}</option>`);
        });
        
        // 恢复之前的选择
        if (currentValue) {
            $select.val(currentValue);
        }
    });
}

// 生成故事创意
function generateStoryIdea() {
    const formData = {
        genre: $('#genre').val(),
        theme: $('#theme').val(),
        length: $('#length').val()
    };
    
    if (!formData.genre) {
        showAlert('请选择小说类型', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('#story-idea-result');
    
    $.ajax({
        url: '/api/story-idea',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#story-idea-result', response.data.story_idea);
            } else {
                showError(response.message || '生成故事创意失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 开发角色
function developCharacter() {
    const formData = {
        character_type: $('#character-type').val(),
        genre: $('#character-genre').val(),
        background: $('#character-background').val()
    };
    
    if (!formData.character_type) {
        showAlert('请选择角色类型', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('#character-result');
    
    $.ajax({
        url: '/api/character',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#character-result', response.data.character_profile);
            } else {
                showError(response.message || '角色开发失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 改进写作
function improveWriting() {
    const formData = {
        text: $('#improve-text').val(),
        improvement_type: $('#improvement-type').val()
    };
    
    if (!formData.text.trim()) {
        showAlert('请输入需要改进的文本', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('#writing-improve-result');
    
    $.ajax({
        url: '/api/improve-writing',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#writing-improve-result', response.data.improved_version);
            } else {
                showError(response.message || '写作改进失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 显示结果
function displayResult(selector, content) {
    const $result = $(selector);
    $result.find('.result-content').text(content);
    $result.show();
    
    // 滚动到结果区域
    $result[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// 显示加载状态
function showLoading(selector) {
    const $result = $(selector);
    $result.find('.result-content').html(`
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">生成中...</span>
            </div>
            <p class="mt-2 mb-0">AI正在思考中，请稍候...</p>
        </div>
    `);
    $result.show();
}

// 显示警告信息
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示警告
    if ($('.alert-container').length === 0) {
        $('main').prepend('<div class="alert-container"></div>');
    }
    
    $('.alert-container').html(alertHtml);
    
    // 自动隐藏
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// 显示错误信息
function showError(message) {
    showAlert(message, 'danger');
}

// 工具函数：格式化文本
function formatText(text) {
    return text.replace(/\n/g, '<br>');
}

// 情节发展建议
function plotDevelopment() {
    const formData = {
        current_plot: $('#current-plot').val(),
        direction: $('#plot-direction').val(),
        style: $('#plot-style').val(),
        constraints: $('#plot-constraints').val()
    };

    if (!formData.current_plot.trim()) {
        showAlert('请输入当前情节描述', 'warning');
        return;
    }

    // 显示加载状态
    showLoading('#plot-development-result');

    $.ajax({
        url: '/api/plot-development',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#plot-development-result', response.data.suggestions);
            } else {
                showError(response.message || '情节发展建议生成失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 写作咨询
function submitConsultation() {
    const formData = {
        question: $('#consultation-question').val(),
        context: $('#consultation-context').val(),
        type: $('#consultation-type').val()
    };

    if (!formData.question.trim()) {
        showAlert('请输入您的问题', 'warning');
        return;
    }

    // 添加用户消息到对话历史
    addConsultationMessage('user', formData.question);

    // 清空输入框
    $('#consultation-question').val('');

    // 显示加载状态
    addConsultationMessage('assistant', '正在思考中...', true);

    $.ajax({
        url: '/api/consultation',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            // 移除加载消息
            $('#consultation-history .loading-message').remove();

            if (response.success) {
                addConsultationMessage('assistant', response.data.answer);
            } else {
                addConsultationMessage('assistant', '抱歉，我现在无法回答您的问题。请稍后再试。');
                showError(response.message || '咨询失败');
            }
        },
        error: function() {
            $('#consultation-history .loading-message').remove();
            addConsultationMessage('assistant', '网络连接出现问题，请稍后再试。');
            showError('请求失败，请检查网络连接');
        }
    });
}

// 获取类型指南
function getGenreGuide() {
    const formData = {
        genre: $('#guide-genre').val()
    };

    if (!formData.genre) {
        showAlert('请选择小说类型', 'warning');
        return;
    }

    // 显示加载状态
    showLoading('#genre-guide-result');

    $.ajax({
        url: '/api/genre-guide',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#genre-guide-result', response.data.guide);
            } else {
                showError(response.message || '类型指南获取失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 发送莎莎消息
function sendSashaMessage() {
    const message = $('#sasha-message').val().trim();

    if (!message) {
        showAlert('请输入消息内容', 'warning');
        return;
    }

    // 添加用户消息
    addSashaMessage('user', message);

    // 清空输入框
    $('#sasha-message').val('');

    // 显示莎莎正在输入
    addSashaMessage('sasha', '莎莎正在输入...', true);

    $.ajax({
        url: '/api/sasha-chat',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ message: message }),
        success: function(response) {
            // 移除加载消息
            $('#sasha-chat-history .loading-message').remove();

            if (response.success) {
                addSashaMessage('sasha', response.data.response);
            } else {
                addSashaMessage('sasha', '抱歉，我现在有点忙，请稍后再和我聊天吧～');
                showError(response.message || '莎莎聊天失败');
            }
        },
        error: function() {
            $('#sasha-chat-history .loading-message').remove();
            addSashaMessage('sasha', '哎呀，网络好像有问题呢，等一下再聊吧～');
            showError('请求失败，请检查网络连接');
        }
    });
}

// 添加咨询消息到对话历史
function addConsultationMessage(role, content, isLoading = false) {
    const timestamp = new Date().toLocaleTimeString();
    const messageClass = role === 'user' ? 'user-message' : 'assistant-message';
    const loadingClass = isLoading ? 'loading-message' : '';

    const messageHtml = `
        <div class="consultation-message ${messageClass} ${loadingClass} mb-3">
            <div class="d-flex">
                <div class="message-avatar me-2">
                    <i class="fas ${role === 'user' ? 'fa-user' : 'fa-robot'}"></i>
                </div>
                <div class="message-content flex-grow-1">
                    <div class="message-header">
                        <strong>${role === 'user' ? '你' : '写作专家'}</strong>
                        <small class="text-muted ms-2">${timestamp}</small>
                    </div>
                    <div class="message-text mt-1">${content}</div>
                </div>
            </div>
        </div>
    `;

    $('#consultation-history').append(messageHtml);
    $('#consultation-history').scrollTop($('#consultation-history')[0].scrollHeight);
}

// 添加莎莎消息
function addSashaMessage(role, content, isLoading = false) {
    const timestamp = new Date().toLocaleTimeString();
    const messageClass = role === 'user' ? 'user-message' : 'sasha-message';
    const loadingClass = isLoading ? 'loading-message' : '';

    const messageHtml = `
        <div class="chat-message ${messageClass} ${loadingClass}">
            <div class="message-avatar">
                <i class="fas ${role === 'user' ? 'fa-user' : 'fa-heart'}"></i>
            </div>
            <div class="message-content">
                <div class="message-bubble ${role === 'user' ? 'user-bubble' : 'sasha-bubble'}">
                    ${content}
                </div>
                <small class="text-muted">${timestamp}</small>
            </div>
        </div>
    `;

    $('#sasha-chat-history').append(messageHtml);
    $('#sasha-chat-history').scrollTop($('#sasha-chat-history')[0].scrollHeight);
}

// 清空咨询历史
function clearConsultationHistory() {
    if (confirm('确定要清空对话历史吗？')) {
        $('#consultation-history').html(`
            <div class="text-center text-muted">
                <i class="fas fa-comments fa-2x mb-2"></i>
                <p>开始您的写作咨询吧！我会根据您的问题提供专业建议。</p>
            </div>
        `);

        // 调用API清空服务器端历史
        $.post('/api/clear-history')
            .done(function(response) {
                if (response.success) {
                    showAlert('对话历史已清空', 'success');
                }
            });
    }
}

// 继续故事发展
function continueStoryDevelopment() {
    const currentResult = $('#plot-development-result .result-content').text();
    if (currentResult) {
        $('#current-plot').val(currentResult);
        showTab('plot-development');
        showAlert('已将建议内容填入当前情节，您可以继续发展', 'info');
    }
}

// 工具函数：复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('内容已复制到剪贴板', 'success');
    }).catch(() => {
        showAlert('复制失败，请手动复制', 'warning');
    });
}
