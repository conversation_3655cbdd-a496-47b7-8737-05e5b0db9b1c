// 主要JavaScript功能

$(document).ready(function() {
    // 初始化应用
    initApp();
    
    // 绑定事件
    bindEvents();
    
    // 加载系统状态
    loadSystemStatus();
    
    // 加载小说类型
    loadGenres();
});

// 初始化应用
function initApp() {
    console.log('小说写作专家应用初始化...');
    
    // 显示欢迎页面
    showTab('welcome');
}

// 绑定事件
function bindEvents() {
    // 侧边栏菜单点击
    $('.list-group-item[data-tab]').click(function(e) {
        e.preventDefault();
        const tabName = $(this).data('tab');
        showTab(tabName);
        
        // 更新菜单状态
        $('.list-group-item').removeClass('active');
        $(this).addClass('active');
    });
    
    // 表单提交事件
    $('#story-idea-form').submit(function(e) {
        e.preventDefault();
        generateStoryIdea();
    });
    
    $('#character-form').submit(function(e) {
        e.preventDefault();
        developCharacter();
    });
    
    $('#writing-improve-form').submit(function(e) {
        e.preventDefault();
        improveWriting();
    });
}

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签页
    $('.tab-content').removeClass('active');
    
    // 显示指定标签页
    $(`#${tabName}-tab`).addClass('active');
    
    // 更新菜单状态
    $('.list-group-item').removeClass('active');
    $(`.list-group-item[data-tab="${tabName}"]`).addClass('active');
}

// 加载系统状态
function loadSystemStatus() {
    $.get('/api/status')
        .done(function(response) {
            if (response.success) {
                displaySystemStatus(response.data);
            } else {
                showError('获取系统状态失败');
            }
        })
        .fail(function() {
            showError('无法连接到服务器');
        });
}

// 显示系统状态
function displaySystemStatus(status) {
    let html = '';
    
    // 初始化状态
    html += createStatusItem('系统初始化', status.initialized);
    html += createStatusItem('知识库', status.knowledge_base_ready);
    html += createStatusItem('AI模型', status.llm_connection);
    
    $('#system-status').html(html);
}

// 创建状态项
function createStatusItem(label, isOnline) {
    const statusClass = isOnline ? 'online' : 'offline';
    const statusText = isOnline ? '正常' : '离线';
    
    return `
        <div class="status-item">
            <span>${label}</span>
            <span>
                <span class="status-indicator ${statusClass}"></span>
                <small class="ms-1">${statusText}</small>
            </span>
        </div>
    `;
}

// 加载小说类型
function loadGenres() {
    $.get('/api/genres')
        .done(function(response) {
            if (response.success) {
                const genres = response.data.genres;
                populateGenreSelects(genres);
            }
        })
        .fail(function() {
            console.log('加载类型列表失败，使用默认类型');
            const defaultGenres = ['现代都市', '古代言情', '玄幻修仙', '科幻未来', '悬疑推理', '青春校园'];
            populateGenreSelects(defaultGenres);
        });
}

// 填充类型选择框
function populateGenreSelects(genres) {
    const selects = ['#genre', '#character-genre'];
    
    selects.forEach(selector => {
        const $select = $(selector);
        const currentValue = $select.val();
        
        // 清空现有选项（保留第一个默认选项）
        $select.find('option:not(:first)').remove();
        
        // 添加类型选项
        genres.forEach(genre => {
            $select.append(`<option value="${genre}">${genre}</option>`);
        });
        
        // 恢复之前的选择
        if (currentValue) {
            $select.val(currentValue);
        }
    });
}

// 生成故事创意
function generateStoryIdea() {
    const formData = {
        genre: $('#genre').val(),
        theme: $('#theme').val(),
        length: $('#length').val()
    };
    
    if (!formData.genre) {
        showAlert('请选择小说类型', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('#story-idea-result');
    
    $.ajax({
        url: '/api/story-idea',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#story-idea-result', response.data.story_idea);
            } else {
                showError(response.message || '生成故事创意失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 开发角色
function developCharacter() {
    const formData = {
        character_type: $('#character-type').val(),
        genre: $('#character-genre').val(),
        background: $('#character-background').val()
    };
    
    if (!formData.character_type) {
        showAlert('请选择角色类型', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('#character-result');
    
    $.ajax({
        url: '/api/character',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#character-result', response.data.character_profile);
            } else {
                showError(response.message || '角色开发失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 改进写作
function improveWriting() {
    const formData = {
        text: $('#improve-text').val(),
        improvement_type: $('#improvement-type').val()
    };
    
    if (!formData.text.trim()) {
        showAlert('请输入需要改进的文本', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('#writing-improve-result');
    
    $.ajax({
        url: '/api/improve-writing',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                displayResult('#writing-improve-result', response.data.improved_version);
            } else {
                showError(response.message || '写作改进失败');
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 显示结果
function displayResult(selector, content) {
    const $result = $(selector);
    $result.find('.result-content').text(content);
    $result.show();
    
    // 滚动到结果区域
    $result[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// 显示加载状态
function showLoading(selector) {
    const $result = $(selector);
    $result.find('.result-content').html(`
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">生成中...</span>
            </div>
            <p class="mt-2 mb-0">AI正在思考中，请稍候...</p>
        </div>
    `);
    $result.show();
}

// 显示警告信息
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示警告
    if ($('.alert-container').length === 0) {
        $('main').prepend('<div class="alert-container"></div>');
    }
    
    $('.alert-container').html(alertHtml);
    
    // 自动隐藏
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// 显示错误信息
function showError(message) {
    showAlert(message, 'danger');
}

// 工具函数：格式化文本
function formatText(text) {
    return text.replace(/\n/g, '<br>');
}

// 工具函数：复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('内容已复制到剪贴板', 'success');
    }).catch(() => {
        showAlert('复制失败，请手动复制', 'warning');
    });
}
