"""
快速功能测试
"""
import requests
import time

def test_basic_endpoints():
    """测试基础端点"""
    print("🔧 快速功能测试")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    # 测试系统状态
    try:
        print("📊 测试系统状态...")
        response = requests.get(f"{base_url}/api/status", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 系统状态正常")
                status = result.get('data', {})
                print(f"   - 初始化: {status.get('initialized')}")
                print(f"   - 知识库: {status.get('knowledge_base_ready')}")
                print(f"   - LLM连接: {status.get('llm_connection')}")
            else:
                print("❌ 系统状态异常")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 系统状态测试失败: {e}")
    
    # 测试类型列表
    try:
        print("\n📚 测试类型列表...")
        response = requests.get(f"{base_url}/api/genres", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                genres = result.get('data', {}).get('genres', [])
                print(f"✅ 获取到 {len(genres)} 个小说类型")
                print(f"   类型: {', '.join(genres[:5])}...")
            else:
                print("❌ 类型列表获取失败")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 类型列表测试失败: {e}")
    
    # 测试莎莎聊天（简单消息）
    try:
        print("\n💕 测试莎莎聊天...")
        data = {"message": "你好莎莎"}
        response = requests.post(f"{base_url}/api/sasha-chat", json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                response_text = result.get('data', {}).get('response', '')
                print(f"✅ 莎莎回复成功 (长度: {len(response_text)})")
                print(f"   回复预览: {response_text[:100]}...")
            else:
                print(f"❌ 莎莎聊天失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 莎莎聊天测试失败: {e}")
    
    # 测试故事创意（简单请求）
    try:
        print("\n🎯 测试故事创意...")
        data = {
            "genre": "现代都市",
            "theme": "友情",
            "length": "短篇"
        }
        response = requests.post(f"{base_url}/api/story-idea", json=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                story_idea = result.get('data', {}).get('story_idea', '')
                print(f"✅ 故事创意生成成功 (长度: {len(story_idea)})")
                print(f"   内容预览: {story_idea[:150]}...")
            else:
                print(f"❌ 故事创意生成失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 故事创意测试失败: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 快速测试完成！")
    print("\n💡 提示:")
    print("- 如果所有测试都通过，说明系统运行正常")
    print("- 如果部分功能失败，可能是API响应较慢，请耐心等待")
    print("- 可以在浏览器中访问 http://localhost:5000 使用完整功能")

if __name__ == '__main__':
    print("⏳ 等待服务启动...")
    time.sleep(3)
    test_basic_endpoints()
