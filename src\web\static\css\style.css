/* 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 功能菜单 */
.list-group-item {
    border: none;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: #007bff;
    color: white;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 特色框 */
.feature-box {
    padding: 30px 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
}

.feature-box h5 {
    color: #333;
    margin-bottom: 15px;
}

.feature-box p {
    color: #666;
    font-size: 0.9rem;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* 结果显示区域 */
.result-content {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 20px;
    border-radius: 0 8px 8px 0;
    margin-top: 15px;
    white-space: pre-wrap;
    line-height: 1.6;
}

/* 系统状态 */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px 20px;
}

.loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .feature-box {
        padding: 20px 10px;
        margin-bottom: 30px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 文本区域 */
textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* 提示信息 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-info {
    background-color: #e3f2fd;
    color: #0277bd;
}

.alert-success {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.alert-warning {
    background-color: #fff3e0;
    color: #f57c00;
}

.alert-danger {
    background-color: #ffebee;
    color: #c62828;
}
