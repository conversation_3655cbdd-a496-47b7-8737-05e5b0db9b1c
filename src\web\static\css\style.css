/* 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 功能菜单 */
.list-group-item {
    border: none;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: #007bff;
    color: white;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 特色框 */
.feature-box {
    padding: 30px 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
}

.feature-box h5 {
    color: #333;
    margin-bottom: 15px;
}

.feature-box p {
    color: #666;
    font-size: 0.9rem;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* 结果显示区域 */
.result-content {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 20px;
    border-radius: 0 8px 8px 0;
    margin-top: 15px;
    white-space: pre-wrap;
    line-height: 1.8;
    font-family: 'Microsoft YaHei', sans-serif;
}

/* 优化结果内容的格式 */
.result-content h1,
.result-content h2,
.result-content h3 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
    font-weight: 600;
}

.result-content h2 {
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.result-content h3 {
    color: #e74c3c;
}

.result-content strong {
    color: #2980b9;
    font-weight: 600;
}

.result-content ul,
.result-content ol {
    margin: 15px 0;
    padding-left: 25px;
}

.result-content li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.result-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-content th,
.result-content td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.result-content th {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    font-weight: 600;
}

.result-content tr:hover {
    background-color: #f8f9fa;
}

.result-content code {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
    display: block;
    margin: 15px 0;
    font-family: 'Consolas', 'Monaco', monospace;
    line-height: 1.5;
}

.result-content blockquote {
    border-left: 4px solid #f39c12;
    background: #fef9e7;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 8px 8px 0;
    font-style: italic;
}

/* 系统状态 */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px 20px;
}

.loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .feature-box {
        padding: 20px 10px;
        margin-bottom: 30px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 文本区域 */
textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* 提示信息 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-info {
    background-color: #e3f2fd;
    color: #0277bd;
}

.alert-success {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.alert-warning {
    background-color: #fff3e0;
    color: #f57c00;
}

.alert-danger {
    background-color: #ffebee;
    color: #c62828;
}

/* 莎莎助手专用样式 */
.btn-pink {
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    border: none;
    color: white;
    font-weight: 600;
}

.btn-pink:hover {
    background: linear-gradient(45deg, #ff5588, #ff7a9a);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-pink {
    border: 2px solid #ff6b9d;
    color: #ff6b9d;
    background: transparent;
}

.btn-outline-pink:hover {
    background: #ff6b9d;
    color: white;
}

/* 聊天消息样式 */
.chat-message {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b9d, #ffc3e0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: linear-gradient(45deg, #007bff, #6c9bd1);
    order: 2;
    margin-right: 0;
    margin-left: 10px;
}

.message-content {
    flex: 1;
    max-width: 80%;
}

.user-message .message-content {
    text-align: right;
}

.message-bubble {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: inline-block;
    max-width: 100%;
}

.sasha-bubble {
    background: linear-gradient(45deg, #fff0f5, #ffeef8);
    border-color: #ff6b9d;
}

.user-bubble {
    background: linear-gradient(45deg, #e3f2fd, #f0f8ff);
    border-color: #007bff;
}

.message-bubble p:last-child {
    margin-bottom: 0;
}

.message-bubble ul {
    padding-left: 20px;
}

.message-bubble li {
    margin-bottom: 5px;
}

/* 头像圆圈 */
.avatar-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b9d, #ffc3e0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* 快速话题按钮 */
.quick-topic {
    transition: all 0.3s ease;
    border-radius: 20px;
}

.quick-topic:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

/* 聊天输入框 */
#sasha-message {
    border-radius: 20px;
    border: 2px solid #ff6b9d;
    resize: none;
}

#sasha-message:focus {
    border-color: #ff5588;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 157, 0.25);
}

/* 渐变背景 */
.bg-gradient {
    background: linear-gradient(45deg, #ff6b9d, #ffc3e0) !important;
}
